package com.color.home.network;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.net.NetworkInfo;
import android.util.Log;

public class NetworkConnectReceiver extends BroadcastReceiver {
    public static boolean DBG = false;
    public static final String TAG = "NetworkConnectReceiver";
    private final NetworkObserver mNetworkObserver;

    public NetworkConnectReceiver(NetworkObserver networkObserver) {
        if (DBG) {
            Log.d(TAG, "networkObserver=" + networkObserver);
        }
        this.mNetworkObserver = networkObserver;
    }

    public void onReceive(Context context, Intent intent) {
        NetworkInfo info;
        if ("android.net.conn.CONNECTIVITY_CHANGE".equals(intent.getAction()) && (info = (NetworkInfo) intent.getParcelableExtra("networkInfo")) != null) {
            if (NetworkInfo.State.CONNECTED == info.getState() && info.isAvailable()) {
                if (DBG) {
                    Log.i(TAG, "---------Internet connected.");
                    Log.i(TAG, "InternetType:" + info.getType() + " connected.");
                }
                if (this.mNetworkObserver != null) {
                    this.mNetworkObserver.reloadContent();
                }
            } else if (this.mNetworkObserver instanceof ExpandNetworkObserver) {
                if (DBG) {
                    Log.i(TAG, "---------Internet disconnected.");
                }
                ((ExpandNetworkObserver) this.mNetworkObserver).networkDisconnect();
            }
        }
    }
}
