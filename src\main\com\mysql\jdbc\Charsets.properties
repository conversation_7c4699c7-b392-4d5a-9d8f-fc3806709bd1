#
# Charset Mappings 
#
#      Java Encoding		MySQL Name (and version, '*' 
#                           denotes preferred value)      
#

javaToMysqlMappings=\
		US-ASCII =			usa7,\
 		US-ASCII =			ascii,\
 		Big5 = 				big5,\
 		GBK = 				gbk,\
 		SJIS = 				sjis,\
 		EUC_CN = 			gb2312,\
 		EUC_JP = 			ujis,\
 		EUC_JP_Solaris = 	>5.0.3 eucjpms,\
 		EUC_KR = 			euc_kr,\
 		EUC_KR = 			>4.1.0 euckr,\
 		ISO8859_1 =			*latin1,\
 		ISO8859_1 =			latin1_de,\
 		ISO8859_1 =			german1,\
 		ISO8859_1 =			danish,\
 		ISO8859_2 =			latin2,\
		ISO8859_2 =			czech,\
		ISO8859_2 =			hungarian,\
		ISO8859_2  =		croat,\
		ISO8859_7  =		greek,\
		ISO8859_7  =		latin7,\
		ISO8859_8  = 		hebrew,\
		ISO8859_9  =		latin5,\
 		ISO8859_13 =		latvian,\
		ISO8859_13 =		latvian1,\
		ISO8859_13 =		estonia,\
		Cp437 =             *>4.1.0 cp850,\
 		Cp437 =				dos,\
 		Cp850 =				Cp850,\
		Cp852 = 			Cp852,\
 		Cp866 = 			cp866,\
 		KOI8_R = 			koi8_ru,\
		KOI8_R = 			>4.1.0 koi8r,\
 		TIS620 = 			tis620,\
		Cp1250 = 			cp1250,\
		Cp1250 = 			win1250,\
		Cp1251 = 			*>4.1.0 cp1251,\
		Cp1251 = 			win1251,\
 		Cp1251 = 			cp1251cias,\
		Cp1251 = 			cp1251csas,\
		Cp1256 = 			cp1256,\
 		Cp1251 = 			win1251ukr,\
		Cp1257 = 			cp1257,\
		MacRoman = 			macroman,\
		MacCentralEurope = 	macce,\
		UTF-8 = 		utf8,\
		UnicodeBig = 	ucs2,\
		US-ASCII =		binary,\
		Cp943 =        	sjis,\
		MS932 =			sjis,\
		MS932 =        	>4.1.11 cp932,\
		WINDOWS-31J =	sjis,\
		WINDOWS-31J = 	>4.1.11 cp932,\
		CP932 =			sjis,\
		CP932 =			*>4.1.11 cp932,\
		SHIFT_JIS = 	sjis,\
		ASCII =			ascii,\
        LATIN5 =		latin5,\
        LATIN7 =		latin7,\
        HEBREW =		hebrew,\
        GREEK =			greek,\
        EUCKR =			euckr,\
        GB2312 =		gb2312,\
        LATIN2 =		latin2
 
#       
# List of multibyte character sets that can not
# use efficient charset conversion or escaping
#
# This map is made case-insensitive inside CharsetMapping
#
#   Java Name			MySQL Name (not currently used)

multibyteCharsets=\
        Big5 = 			big5,\
 		GBK = 			gbk,\
 		SJIS = 			sjis,\
 		EUC_CN = 		gb2312,\
 		EUC_JP = 		ujis,\
 		EUC_JP_Solaris = eucjpms,\
 		EUC_KR = 		euc_kr,\
 		EUC_KR = 		>4.1.0 euckr,\
 		Cp943 =        	sjis,\
		Cp943 = 		cp943,\
		WINDOWS-31J =	sjis,\
		WINDOWS-31J = 	cp932,\
		CP932 =			cp932,\
		MS932 =			sjis,\
		MS932 =        	cp932,\
		SHIFT_JIS = 	sjis,\
		EUCKR =			euckr,\
        GB2312 =		gb2312,\
		UTF-8 = 		utf8,\
		utf8 =          utf8,\
		UnicodeBig = 	ucs2