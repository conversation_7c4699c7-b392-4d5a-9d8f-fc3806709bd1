#*
#*******************************************************************************
# Copyright (C) 2016 and later: Unicode, Inc. and others.                      *
# License & terms of use: http://www.unicode.org/copyright.html        *
#*******************************************************************************
#*******************************************************************************
#* Copyright (C) 2008-2014, International Business Machines Corporation and    *
#* others. All Rights Reserved.                                                *
#*******************************************************************************
#* This is the properties file which contains ICU runtime configuration.
#*

#
# The default TimeZone implementation type used by the ICU TimeZone
# factory method. [ ICU | JDK ]
# @stable ICU 4.0
com.ibm.icu.util.TimeZone.DefaultTimeZoneType = ICU


# The default mode for when an apostrophe starts quoted literal text for
# MessageFormat output. See the API documentation for com.ibm.icu.text.MessagePatter
# for the details. [ DOUBLE_OPTIONAL | DOUBLE_REQUIRED ]
# @stable ICU 4.8
com.ibm.icu.text.MessagePattern.ApostropheMode = DOUBLE_OPTIONAL

#
# [Internal Use Only]
# By default, DecimalFormat uses some internal equivalent character
# data in addition to ones in DecimalFormatSymbols for parsing
# decimal/grouping separators.  When this property is true,
# DecimalFormat uses separators configured by DecimalFormatSymbols only
# and does not try to find a match in the internal equivalent character
# data.
# @internal
com.ibm.icu.text.DecimalFormat.SkipExtendedSeparatorParsing = false

# File system path where ICU looks for binary data files.
# If not empty, then ICU looks for binary data files before looking for data on the classpath.
# This string may contain multiple paths, see File.pathSeparatorChar.
# Spaces (U+0020) around each path are trimmed away. Empty paths are ignored.
# There may be individual files, for example, zoneinfo64.res,
# or ICU4C .dat package files, for example, collation.dat or icudt54l.dat.
# Each ICU data file may contain little-endian or big-endian data.
# Each ICU data file's charset must be ASCII. (Platform type 'l' or 'b' but not 'e'.)
# @draft ICU 54
com.ibm.icu.impl.ICUBinary.dataPath =

#
# [Internal Use Only]
# Disable resource path scan for building full locale name list
# at run time.
# @internal
com.ibm.icu.impl.ICUResourceBundle.skipRuntimeLocaleResourceScan = false

#
# [Internal Use Only]
# Time zone names service factory
# @internal
# com.ibm.icu.text.TimeZoneNames.Factory.impl = com.ibm.icu.impl.TimeZoneNamesFactoryImpl

#
# [Internal Use Only]
# LocaleDisplayNames implementation class
# @internal
# com.ibm.icu.text.LocaleDisplayNames.impl = com.ibm.icu.impl.LocaleDisplayNamesImpl
