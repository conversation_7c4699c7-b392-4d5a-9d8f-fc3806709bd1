# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#  
#      http://www.apache.org/licenses/LICENSE-2.0
#  
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# 

# messages for EN locale
beans.00=no getter for {0} property
beans.01=no property for name {0} is found
beans.02=in DefaultPersistenceDelegate.mutatesTo() {0} : {1}
beans.03=Target Bean class is null
beans.04=bad property name
beans.05=Modifier for setter method should be public.
beans.06=Number of parameters in setter method is not equal to 1.
beans.07=Parameter type in setter method does not corresponds to predefined.
beans.08=Number of parameters in getter method is not equal to 0.
beans.09=Parameter type in getter method does not corresponds to predefined.
beans.0A=Modifier for getter method should be public.
beans.0B=Exception in command execution
beans.0C=source is null
beans.0D=Error in expression: {0}
beans.0E=Changes are null
beans.0F=The new BeanContext can not be set
beans.10=no node is found for statement with target = {0}
beans.11=no getter for property {0} found
beans.12=cannot access property {0} getter
beans.13=no setter for property {0} found
beans.14=Exception while finding property descriptor
beans.15=The listener is null
beans.16=The provider is null
beans.17=The child is null
beans.18=The requestor is null
beans.19=The service class is null
beans.1A=The service selector is null
beans.1B=The service is null
beans.1C=The event is null
beans.1D=bean is null
beans.1E=Illegal class name: {0}
beans.1F=Method not found: get{0}
beans.20=Method not found: set{0}
beans.21=Modifier for indexed getter method should be public.
beans.22=Number of parameters in getter method is not equal to 1.
beans.23=Parameter in indexed getter method is not of integer type.
beans.24=Parameter type in indexed getter method does not correspond to predefined.
beans.25=Modifier for indexed setter method should be public.
beans.26=Number of parameters in indexed setter method is not equal to 2.
beans.27=First parameter type in indexed setter method should be int.
beans.28=Second parameter type in indexed setter method does not corresponds to predefined.
beans.29=Membership listener is null
beans.2A=Target child can not be null
beans.2B=Resource name can not be null
beans.2C=The child can not be null
beans.2D=Invalid resource
beans.2E=PropertyVetoException was thrown while removing a child: {0}; Original error message:{1}
beans.2F=Target child is null
beans.30=PropertyVetoException was thrown while adding a child: {0}; Original error message:{1}
beans.31=No valid method {0} for {1} found.
beans.32=Cannot acquire event type from {0} listener.
beans.33={0} does not return <void>
beans.34={0} should have a single input parameter
beans.35=Single parameter does not match to {0} class
beans.36=No input params are allowed for getListenerMethod
beans.37=Return type of getListenerMethod is not an array of listeners
beans.38=Add and remove methods are not available
beans.39=Cannot generate event set descriptor for name {0}.
beans.3A=Event type with name {0} is not found.
beans.3B=skipping expression {0}...
beans.3C=Unknown method name for array
beans.3D=First parameter in array getter(setter) is not of Integer type
beans.3E=Illegal number of arguments in array getter
beans.3F=Illegal number of arguments in array setter
beans.40=No constructor for class {0} found
beans.41=No method with name {0} is found
beans.42=target is not generated: classname {0} is not found
beans.43=Cannot convert {0} to char
beans.44=for property {0} no getter(setter) is found
beans.45=method name is not generated: error in getMethodName()
beans.46=Not a valid child
beans.47=Unable to instantiate property editor
beans.48=Property editor is not assignable from the PropertyEditor interface
beans.49=Child cannot implement both BeanContextChild and BeanContextProxy
beans.4A=newInstance is null
beans.4B=type is null
beans.4C=encoder is null
beans.4D=Invalid method call
beans.4E=stopClass is not ancestor of beanClass
beans.4F=search path is null
beans.50=not an indexed property
beans.51=Listener method {0} should have parameter of type {1}
beans.52=listenerMethodName(s) is null
beans.53=eventSetName is null
beans.54=listenerType is null
beans.55=Method is null
beans.56=Provider does not match
beans.57=Property type is incompatible with the indexed property type
beans.58=No such indexed read method
beans.59=Security violation accessing indexed read method
beans.5A=Indexed read method is not compatible with indexed write method
beans.5B=Indexed read method must take a single int argument
beans.5C=Security violation accessing indexed write method
beans.5D=No such indexed write method
beans.5E=Indexed method is not compatible with non indexed method
beans.5F=Indexed write method must take a two arguments
beans.60=Indexed write method must take an int as its first argument
beans.61=Indexed write method is not compatible with indexed read method
