package android.support.p001v4.media;

import android.media.VolumeProvider;
import android.support.annotation.RequiresApi;

/* access modifiers changed from: package-private */
@RequiresApi(21)
/* renamed from: android.support.v4.media.VolumeProviderCompatApi21 */
public class VolumeProviderCompatApi21 {

    /* renamed from: android.support.v4.media.VolumeProviderCompatApi21$Delegate */
    public interface Delegate {
        void onAdjustVolume(int i);

        void onSetVolumeTo(int i);
    }

    VolumeProviderCompatApi21() {
    }

    public static Object createVolumeProvider(int volumeControl, int maxVolume, int currentVolume, final Delegate delegate) {
        return new VolumeProvider(volumeControl, maxVolume, currentVolume) {
            /* class android.support.p001v4.media.VolumeProviderCompatApi21.C01171 */

            public void onSetVolumeTo(int volume) {
                delegate.onSetVolumeTo(volume);
            }

            public void onAdjustVolume(int direction) {
                delegate.onAdjustVolume(direction);
            }
        };
    }

    public static void setCurrentVolume(Object volumeProviderObj, int currentVolume) {
        ((VolumeProvider) volumeProviderObj).setCurrentVolume(currentVolume);
    }
}
