#
# Fixed
#

ResultSet.Retrieved__1=Retrieved 
ResultSet.Bad_format_for_BigDecimal=Bad format for BigDecimal ''{0}'' in column {1}.
ResultSet.Bad_format_for_BigInteger=Bad format for BigInteger ''{0}'' in column {1}.
ResultSet.Column_Index_out_of_range_low=Column Index out of range, {0} < 1.
ResultSet.Column_Index_out_of_range_high=Column Index out of range, {0} > {1}. 
ResultSet.Value_is_out_of_range=Value ''{0}'' is out of range [{1}, {2}].
ResultSet.Positioned_Update_not_supported=Positioned Update not supported.
ResultSet.Bad_format_for_Date=Bad format for DATE ''{0}'' in column {1}.
ResultSet.Bad_format_for_Column=Bad format for {0} ''{1}'' in column {2} ({3}).
ResultSet.Bad_format_for_number=Bad format for number ''{0}'' in column {1}.
ResultSet.Illegal_operation_on_empty_result_set=Illegal operation on empty result set.

Statement.0=Connection is closed.
Statement.2=Unsupported character encoding ''{0}''
Statement.5=Illegal value for setFetchDirection().
Statement.7=Illegal value for setFetchSize().
Statement.11=Illegal value for setMaxFieldSize().
Statement.13=Can not set max field size > max allowed packet of {0} bytes.
Statement.15=setMaxRows() out of range. 
Statement.19=Illegal flag for getMoreResults(int).
Statement.21=Illegal value for setQueryTimeout().
Statement.27=Connection is read-only. 
Statement.28=Queries leading to data modification are not allowed.
Statement.34=Connection is read-only. 
Statement.35=Queries leading to data modification are not allowed.
Statement.40=Can not issue INSERT/UPDATE/DELETE with executeQuery().
Statement.42=Connection is read-only. 
Statement.43=Queries leading to data modification are not allowed.
Statement.46=Can not issue SELECT via executeUpdate() or executeLargeUpdate().
Statement.49=No operations allowed after statement closed.
Statement.57=Can not issue data manipulation statements with executeQuery().
Statement.59=Can not issue NULL query.
Statement.61=Can not issue empty query.
Statement.63=Statement not closed explicitly. You should call close() on created Statement instances from your code to be more efficient.
Statement.GeneratedKeysNotRequested=Generated keys not requested. You need to specify Statement.RETURN_GENERATED_KEYS to Statement.executeUpdate(), Statement.executeLargeUpdate() or Connection.prepareStatement().
Statement.ConnectionKilledDueToTimeout=Connection closed to due to statement timeout being reached and "queryTimeoutKillsConnection" being set to "true".
UpdatableResultSet.1=Can not call deleteRow() when on insert row.
UpdatableResultSet.2=Can not call deleteRow() on empty result set.
UpdatableResultSet.3=Before start of result set. Can not call deleteRow().
UpdatableResultSet.4=After end of result set. Can not call deleteRow().
UpdatableResultSet.7=Not on insert row.
UpdatableResultSet.8=Can not call refreshRow() when on insert row.
UpdatableResultSet.9=Can not call refreshRow() on empty result set.
UpdatableResultSet.10=Before start of result set. Can not call refreshRow().
UpdatableResultSet.11=After end of result set. Can not call refreshRow().
UpdatableResultSet.12=refreshRow() called on row that has been deleted or had primary key changed.
UpdatableResultSet.34=Updatable result set created, but never updated. You should only create updatable result sets when you want to update/insert/delete values using the updateRow(), deleteRow() and insertRow() methods.
UpdatableResultSet.43=Can not create updatable result sets when there is no currently selected database and MySQL server version < 4.1.
UpdatableResultSet.44=Can not call updateRow() when on insert row.

#
# Possible re-names
#

ResultSet.Query_generated_no_fields_for_ResultSet_57=Query generated no fields for ResultSet
ResultSet.Illegal_value_for_fetch_direction_64=Illegal value for fetch direction
ResultSet.Value_must_be_between_0_and_getMaxRows()_66=Value must be between 0 and getMaxRows()
ResultSet.Query_generated_no_fields_for_ResultSet_99=Query generated no fields for ResultSet
ResultSet.Operation_not_allowed_after_ResultSet_closed_144=Operation not allowed after ResultSet closed
ResultSet.Before_start_of_result_set_146=Before start of result set
ResultSet.After_end_of_result_set_148=After end of result set
ResultSet.Query_generated_no_fields_for_ResultSet_133=Query generated no fields for ResultSet
ResultSet.ResultSet_is_from_UPDATE._No_Data_115=ResultSet is from UPDATE. No Data.

#
# To fix
#

ResultSet.Invalid_value_for_getFloat()_-____68=Invalid value for getFloat() - \'
ResultSet.Invalid_value_for_getInt()_-____74=Invalid value for getInt() - \'
ResultSet.Invalid_value_for_getLong()_-____79=Invalid value for getLong() - \'
ResultSet.Invalid_value_for_getFloat()_-____200=Invalid value for getFloat() - \'
ResultSet.___in_column__201=\' in column 
ResultSet.Invalid_value_for_getInt()_-____206=Invalid value for getInt() - \'
ResultSet.___in_column__207=\' in column 
ResultSet.Invalid_value_for_getLong()_-____211=Invalid value for getLong() - \'
ResultSet.___in_column__212=\' in column 
ResultSet.Invalid_value_for_getShort()_-____217=Invalid value for getShort() - \'
ResultSet.___in_column__218=\' in column 

ResultSet.Class_not_found___91=Class not found: 
ResultSet._while_reading_serialized_object_92=\ while reading serialized object

ResultSet.Invalid_value_for_getShort()_-____96=Invalid value for getShort() - \'
ResultSet.Unsupported_character_encoding____101=Unsupported character encoding \'

ResultSet.Malformed_URL____104=Malformed URL \'
ResultSet.Malformed_URL____107=Malformed URL \'
ResultSet.Malformed_URL____141=Malformed URL \'

ResultSet.Column____112=Column \'
ResultSet.___not_found._113=\' not found.

ResultSet.Unsupported_character_encoding____135=Unsupported character encoding \'
ResultSet.Unsupported_character_encoding____138=Unsupported character encoding \'

#
# Usage advisor messages for ResultSets
#

ResultSet.ResultSet_implicitly_closed_by_driver=ResultSet implicitly closed by driver.\n\nYou should close ResultSets explicitly from your code to free up resources in a more efficient manner.
ResultSet.Possible_incomplete_traversal_of_result_set=Possible incomplete traversal of result set. Cursor was left on row {0} of {1} rows when it was closed.\n\nYou should consider re-formulating your query to return only the rows you are interested in using.
ResultSet.The_following_columns_were_never_referenced=The following columns were part of the SELECT statement for this result set, but were never referenced: {0}
ResultSet.Too_Large_Result_Set=Result set size of {0} rows is larger than \"resultSetSizeThreshold\" of {1} rows. Application may be requesting more data than it is using. Consider reformulating the query.
ResultSet.CostlyConversion=ResultSet type conversion via parsing detected when calling {0} for column {1} (column named ''{2}'') in table ''{3}''{4}\n\nJava class of column type is ''{5}'', MySQL field type is ''{6}''.\n\nTypes that could be converted directly without parsing are:\n{7}
ResultSet.CostlyConversionCreatedFromQuery= created from query:\n\n

ResultSet.Value____173=Value \'
ResultSetMetaData.46=Column index out of range.
ResultSet.___is_out_of_range_[-127,127]_174=\' is out of range [-127,127]
ResultSet.Bad_format_for_Date____180=Bad format for Date \'

ResultSet.Timestamp_too_small_to_convert_to_Time_value_in_column__223=Timestamp too small to convert to Time value in column 
ResultSet.Precision_lost_converting_TIMESTAMP_to_Time_with_getTime()_on_column__227=Precision lost converting TIMESTAMP to Time with getTime() on column 
ResultSet.Precision_lost_converting_DATETIME_to_Time_with_getTime()_on_column__230=Precision lost converting DATETIME to Time with getTime() on column 
ResultSet.Bad_format_for_Time____233=Bad format for Time \'
ResultSet.___in_column__234=\' in column 
ResultSet.Bad_format_for_Timestamp____244=Bad format for Timestamp \'
ResultSet.___in_column__245=\' in column 
ResultSet.Cannot_convert_value____249=Cannot convert value \'
ResultSet.___from_column__250=\' from column 
ResultSet._)_to_TIMESTAMP._252=\ ) to TIMESTAMP.
ResultSet.Timestamp_too_small_to_convert_to_Time_value_in_column__257=Timestamp too small to convert to Time value in column 
ResultSet.Precision_lost_converting_TIMESTAMP_to_Time_with_getTime()_on_column__261=Precision lost converting TIMESTAMP to Time with getTime() on column 
ResultSet.Precision_lost_converting_DATETIME_to_Time_with_getTime()_on_column__264=Precision lost converting DATETIME to Time with getTime() on column 
ResultSet.Bad_format_for_Time____267=Bad format for Time \'
ResultSet.___in_column__268=\' in column 
ResultSet.Bad_format_for_Timestamp____278=Bad format for Timestamp \'
ResultSet.___in_column__279=\' in column 
ResultSet.Cannot_convert_value____283=Cannot convert value \'
ResultSet.___from_column__284=\' from column 
ResultSet._)_to_TIMESTAMP._286=\ ) to TIMESTAMP.
CallableStatement.2=Parameter name can not be NULL or zero-length.
CallableStatement.3=No parameter named '
CallableStatement.4='
CallableStatement.5=Parameter named '
CallableStatement.6=' is not an OUT parameter
CallableStatement.7=No output parameters registered.
CallableStatement.8=No output parameters returned by procedure.
CallableStatement.9=Parameter number 
CallableStatement.10=\ is not an OUT parameter
CallableStatement.11=Parameter index of 
CallableStatement.12=\ is out of range (1, 
CallableStatement.13=)
CallableStatement.14=Can not use streaming result sets with callable statements that have output parameters
CallableStatement.1=Unable to retrieve metadata for procedure.
CallableStatement.0=Parameter name can not be 
CallableStatement.15=null.
CallableStatement.16=empty.
CallableStatement.21=Parameter 
CallableStatement.22=\ is not registered as an output parameter
CommunicationsException.2=\ is longer than the server configured value of 
CommunicationsException.3='wait_timeout'
CommunicationsException.4='interactive_timeout'
CommunicationsException.5=may or may not be greater than the server-side timeout 
CommunicationsException.6=(the driver was unable to determine the value of either the 
CommunicationsException.7='wait_timeout' or 'interactive_timeout' configuration values from 
CommunicationsException.8=the server.
CommunicationsException.11=. You should consider either expiring and/or testing connection validity 
CommunicationsException.12=before use in your application, increasing the server configured values for client timeouts, 
CommunicationsException.13=or using the Connector/J connection property 'autoReconnect=true' to avoid this problem.
CommunicationsException.TooManyClientConnections=The driver was unable to create a connection due to an inability to establish the client portion of a socket.\n\nThis is usually caused by a limit on the number of sockets imposed by the operating system. This limit is usually configurable. \n\nFor Unix-based platforms, see the manual page for the 'ulimit' command. Kernel or system reconfiguration may also be required.\n\nFor Windows-based platforms, see Microsoft Knowledge Base Article 196271 (Q196271).
CommunicationsException.LocalSocketAddressNotAvailable=The configuration parameter \"localSocketAddress\" has been set to a network interface not available for use by the JVM.
CommunicationsException.20=Communications link failure
CommunicationsException.ClientWasStreaming=Application was streaming results when the connection failed. Consider raising value of 'net_write_timeout' on the server.
CommunicationsException.ServerPacketTimingInfoNoRecv=The last packet sent successfully to the server was {0} milliseconds ago. The driver has not received any packets from the server.
CommunicationsException.ServerPacketTimingInfo=The last packet successfully received from the server was {0} milliseconds ago.  The last packet sent successfully to the server was {1} milliseconds ago.
CommunicationsException.TooManyAuthenticationPluginNegotiations=Too many authentication plugin negotiations.
NonRegisteringDriver.1=The url cannot be null
NonRegisteringDriver.3=Hostname of MySQL Server
NonRegisteringDriver.7=Port number of MySQL Server
NonRegisteringDriver.13=Username to authenticate as
NonRegisteringDriver.16=Password to use for authentication
NonRegisteringDriver.17=Cannot load connection class because of underlying exception: '
NonRegisteringDriver.18='.
NonRegisteringDriver.37=Must specify port after ':' in connection string
MysqlDataSource.BadUrl=Failed to get a connection using the URL ''{0}''.
SQLError.35=Disconnect error
SQLError.36=Data truncated
SQLError.37=Privilege not revoked
SQLError.38=Invalid connection string attribute
SQLError.39=Error in row
SQLError.40=No rows updated or deleted
SQLError.41=More than one row updated or deleted
SQLError.42=Wrong number of parameters
SQLError.43=Unable to connect to data source
SQLError.44=Connection in use
SQLError.45=Connection not open
SQLError.46=Data source rejected establishment of connection
SQLError.47=Connection failure during transaction
SQLError.48=Communication link failure
SQLError.49=Insert value list does not match column list
SQLError.50=Numeric value out of range
SQLError.51=Datetime field overflow
SQLError.52=Division by zero
SQLError.53=Deadlock found when trying to get lock; Try restarting transaction
SQLError.54=Invalid authorization specification
SQLError.55=Syntax error or access violation
SQLError.56=Base table or view not found
SQLError.57=Base table or view already exists
SQLError.58=Base table not found
SQLError.59=Index already exists
SQLError.60=Index not found
SQLError.61=Column already exists
SQLError.62=Column not found
SQLError.63=No default for column
SQLError.64=General error
SQLError.65=Memory allocation failure
SQLError.66=Invalid column number
SQLError.67=Invalid argument value
SQLError.68=Driver not capable
SQLError.69=Timeout expired
ChannelBuffer.0=Unsupported character encoding '
ChannelBuffer.1='
Field.12=Unsupported character encoding '
Field.13='

Blob.0=indexToWriteAt must be >= 1
Blob.1=IO Error while writing bytes to blob
Blob.2=Position 'pos' can not be < 1
Blob.invalidStreamLength=Requested stream length of {2} is out of range, given blob length of {0} and starting position of {1}.
Blob.invalidStreamPos=Position 'pos' can not be < 1 or > blob length.

StringUtils.0=Unsupported character encoding '
StringUtils.1='.
StringUtils.5=Unsupported character encoding '
StringUtils.6='.
StringUtils.10=Unsupported character encoding '
StringUtils.11='.
StringUtils.15=Illegal argument value {0} for openingMarkers and/or {1} for closingMarkers. These cannot be null and must have the same length.
StringUtils.16=Illegal argument value {0} for overridingMarkers. These cannot be null and must be a sub-set of openingMarkers {1}.

RowDataDynamic.1=WARN: Possible incomplete traversal of result set. Streaming result set had {0} rows left to read when it was closed.\n\nYou should consider re-formulating your query to return only the rows you are interested in using.\n\nResultSet was created at: {1} 
RowDataDynamic.2=Error retrieving record: Unexpected Exception: {0} message given: {1}\n\nNested Stack Trace:\n{2}
RowDataDynamic.3=Operation not supported for streaming result sets

Clob.0=indexToWriteAt must be >= 1
Clob.1=indexToWriteAt must be >= 1
Clob.2=Starting position can not be < 1
Clob.3=String to set can not be NULL
Clob.4=Starting position can not be < 1
Clob.5=String to set can not be NULL
Clob.6=CLOB start position can not be < 1
Clob.7=CLOB start position + length can not be > length of CLOB
Clob.8=Illegal starting position for search, '
Clob.9='
Clob.10=Starting position for search is past end of CLOB
Clob.11=Cannot truncate CLOB of length 
Clob.12=\ to length of 
Clob.13=.
PacketTooBigException.0=Packet for query is too large (
PacketTooBigException.1=\ > 
PacketTooBigException.2=). 
PacketTooBigException.3=You can change this value on the server by setting the 
PacketTooBigException.4=max_allowed_packet' variable.
Util.1=\n\n** BEGIN NESTED EXCEPTION ** \n\n
Util.2=\nMESSAGE: 
Util.3=\n\nSTACKTRACE:\n\n
Util.4=\n\n** END NESTED EXCEPTION **\n\n
MiniAdmin.0=Conection can not be null.
MiniAdmin.1=MiniAdmin can only be used with MySQL connections
NamedPipeSocketFactory.2=Can not specify NULL or empty value for property '
NamedPipeSocketFactory.3='.
NamedPipeSocketFactory.4=Named pipe path can not be null or empty
MysqlIO.1=Unexpected end of input stream
MysqlIO.2=Reading packet of length 
MysqlIO.3=\nPacket header:\n
MysqlIO.4=readPacket() payload:\n
MysqlIO.8=Slow query explain results for '
MysqlIO.9=' :\n\n
MysqlIO.10=\ message from server: "
MysqlIO.15=SSL Connection required, but not supported by server.
MysqlIO.17=Attempt to close streaming result set 
MysqlIO.18=\ when no streaming  result set was registered. This is an internal error.
MysqlIO.19=Attempt to close streaming result set 
MysqlIO.20=\ that was not registered.
MysqlIO.21=\ Only one streaming result set may be open and in use per-connection. Ensure that you have called .close() on 
MysqlIO.22=\ any active result sets before attempting more queries.
MysqlIO.23=Can not use streaming results with multiple result statements
MysqlIO.25=\ ... (truncated)
MysqlIO.SlowQuery=Slow query (exceeded {0} {1}, duration: {2} {1}): {3}
MysqlIO.ServerSlowQuery=The server processing the query has indicated that the query was marked "slow". 
Nanoseconds=ns
Milliseconds=ms
MysqlIO.28=Not issuing EXPLAIN for query of size > 
MysqlIO.29=\ bytes.
MysqlIO.33=The following query was executed with a bad index, use 'EXPLAIN' for more details: 
MysqlIO.35=The following query was executed using no index, use 'EXPLAIN' for more details: 
MysqlIO.36=\n\nLarge packet dump truncated at 
MysqlIO.37=\ bytes.
MysqlIO.39=Streaming result set 
MysqlIO.40=\ is still active.
MysqlIO.41=\ No statements may be issued when any streaming result sets are open and in use on a given connection.
MysqlIO.42=\ Ensure that you have called .close() on any active streaming result sets before attempting more queries.
MysqlIO.43=Unexpected end of input stream
MysqlIO.44=Reading reusable packet of length 
MysqlIO.45=\nPacket header:\n
MysqlIO.46=reuseAndReadPacket() payload:\n
MysqlIO.47=Unexpected end of input stream
MysqlIO.48=Unexpected end of input stream
MysqlIO.49=Packets received out of order
MysqlIO.50=Short read from server, expected 
MysqlIO.51=\ bytes, received only 
MysqlIO.57=send() compressed packet:\n
MysqlIO.58=\n\nOriginal packet (uncompressed):\n
MysqlIO.59=send() packet payload:\n
MysqlIO.60=Unable to open file 
MysqlIO.63=for 'LOAD DATA LOCAL INFILE' command.
MysqlIO.64=Due to underlying IOException: 
MysqlIO.65=Unable to close local file during LOAD DATA LOCAL INFILE command
MysqlIO.68=\ message from server: "
MysqlIO.70=Unknown column
MysqlIO.72=\ message from server: "
MysqlIO.75=No name specified for socket factory
MysqlIO.76=Could not create socket factory '
MysqlIO.77=' due to underlying exception: 
MysqlIO.79=Unexpected end of input stream
MysqlIO.80=Unexpected end of input stream
MysqlIO.81=Unexpected end of input stream
MysqlIO.82=Unexpected end of input stream
MysqlIO.83=Packets received out of order
MysqlIO.84=Packets received out of order
MysqlIO.85=Unexpected end of input stream
MysqlIO.86=Unexpected end of input stream
MysqlIO.87=Unexpected end of input stream
MysqlIO.88=Packets received out of order
MysqlIO.89=Packets received out of order
MysqlIO.91=Failed to create message digest 'SHA-1' for authentication. 
MysqlIO.92=\ You must use a JDK that supports JCE to be able to use secure connection authentication
MysqlIO.97=Unknown type '
MysqlIO.98=\ in column 
MysqlIO.99=\ of 
MysqlIO.100=\ in binary-encoded result set.
MysqlIO.102=, underlying cause: 
MysqlIO.103=Unexpected packet length 
MysqlIO.EOF=Can not read response from server. Expected to read {0} bytes, read {1} bytes before connection was unexpectedly lost.
MysqlIO.NoInnoDBStatusFound=No InnoDB status output returned by server.
MysqlIO.InnoDBStatusFailed=Couldn't retrieve InnoDB status due to underlying exception: 
MysqlIO.LoadDataLocalNotAllowed=Server asked for stream in response to LOAD DATA LOCAL INFILE but functionality is disabled at client by 'allowLoadLocalInfile' being set to 'false'.
MysqlIO.SSLWarning=Establishing SSL connection without server's identity verification is not recommended. According to MySQL 5.5.45+, 5.6.26+ and 5.7.6+ requirements SSL connection must be established by default if explicit option isn't set. For compliance with existing applications not using SSL the verifyServerCertificate property is set to 'false'. You need either to explicitly disable SSL by setting useSSL=false, or set useSSL=true and provide truststore for server certificate verification.
NotImplemented.0=Feature not implemented
UnsupportedSQLType.0=Unsupported SQL type: 
PreparedStatement.0=SQL String can not be NULL
PreparedStatement.1=SQL String can not be NULL
PreparedStatement.2=Parameter index out of range (
PreparedStatement.3=\ > 
PreparedStatement.4=)
PreparedStatement.16=Unknown Types value
PreparedStatement.17=Cannot convert 
PreparedStatement.18=\ to SQL type requested due to 
PreparedStatement.19=\ - 
PreparedStatement.20=Connection is read-only. 
PreparedStatement.21=Queries leading to data modification are not allowed
PreparedStatement.25=Connection is read-only. 
PreparedStatement.26=Queries leading to data modification are not allowed
PreparedStatement.32=Unsupported character encoding '
PreparedStatement.33='
PreparedStatement.34=Connection is read-only. 
PreparedStatement.35=Queries leading to data modification are not allowed
PreparedStatement.37=Can not issue executeUpdate() or executeLargeUpdate() for SELECTs
PreparedStatement.40=No value specified for parameter 
PreparedStatement.43=PreparedStatement created, but used 1 or fewer times. It is more efficient to prepare statements once, and re-use them many times
PreparedStatement.48=PreparedStatement has been closed. No further operations allowed.
PreparedStatement.49=Parameter index out of range (
PreparedStatement.50=\ < 1 ).
PreparedStatement.51=Parameter index out of range (
PreparedStatement.52=\ > number of parameters, which is 
PreparedStatement.53=).
PreparedStatement.54=Invalid argument value: 
PreparedStatement.55=Error reading from InputStream 
PreparedStatement.56=Error reading from InputStream 
PreparedStatement.61=SQL String can not be NULL
ServerPreparedStatement.2=Connection is read-only. 
ServerPreparedStatement.3=Queries leading to data modification are not allowed
ServerPreparedStatement.6=\ unable to materialize as string due to underlying SQLException: 
ServerPreparedStatement.7=Not supported for server-side prepared statements.
ServerPreparedStatement.8=No parameters defined during prepareCall()
ServerPreparedStatement.9=Parameter index out of bounds. 
ServerPreparedStatement.10=\ is not between valid values of 1 and 
ServerPreparedStatement.11=Driver can not re-execute prepared statement when a parameter has been changed 
ServerPreparedStatement.12=from a streaming type to an intrinsic data type without calling clearParameters() first.
ServerPreparedStatement.13=Statement parameter 
ServerPreparedStatement.14=\ not set.
ServerPreparedStatement.15=Slow query (exceeded {0} ms, duration: {1} ms) as prepared: {2}\n\n with parameters bound:\n\n{3}
ServerPreparedStatement.18=Unknown LONG DATA type '
ServerPreparedStatement.22=Unsupported character encoding '
ServerPreparedStatement.24=Error while reading binary stream: 
ServerPreparedStatement.25=Error while reading binary stream: 
ByteArrayBuffer.0=ByteArrayBuffer has no NIO buffers
ByteArrayBuffer.1=Unsupported character encoding '
ByteArrayBuffer.2=Buffer length is less then "expectedLength" value.
AssertionFailedException.0=ASSERT FAILS: Exception 
AssertionFailedException.1=\ that should not be thrown, was thrown
NotUpdatable.0=Result Set not updatable.
NotUpdatable.1=This result set must come from a statement 
NotUpdatable.2=that was created with a result set type of ResultSet.CONCUR_UPDATABLE, 
NotUpdatable.3=the query must select only one table, can not use functions and must 
NotUpdatable.4=select all primary keys from that table. See the JDBC 2.1 API Specification, 
NotUpdatable.5=section 5.6 for more details.
NotUpdatableReason.0=Result Set not updatable (references more than one table).
NotUpdatableReason.1=Result Set not updatable (references more than one database).
NotUpdatableReason.2=Result Set not updatable (references no tables).
NotUpdatableReason.3=Result Set not updatable (references computed values or doesn't reference any columns or tables).
NotUpdatableReason.4=Result Set not updatable (references no primary keys).
NotUpdatableReason.5=Result Set not updatable (referenced table has no primary keys).
NotUpdatableReason.6=Result Set not updatable (references unknown primary key {0}).
NotUpdatableReason.7=Result Set not updatable (does not reference all primary keys).

JDBC4Connection.ClientInfoNotImplemented=Configured clientInfoProvider class ''{0}'' does not implement com.mysql.jdbc.JDBC4ClientInfoProvider.

InvalidLoadBalanceStrategy=Invalid load balancing strategy ''{0}''.
Connection.BadValueInServerVariables=Invalid value ''{1}'' for server variable named ''{0}'', falling back to sane default of ''{2}''.

Connection.UnableToConnect=Could not create connection to database server.
Connection.UnableToConnectWithRetries=Could not create connection to database server. \
Attempted reconnect {0} times. Giving up.
Connection.UnexpectedException=Unexpected exception encountered during query.
Connection.UnhandledExceptionDuringShutdown=Unexpected exception during server shutdown.

MysqlClearPasswordPlugin.1=Unsupported character encoding ''{0}'' for ''passwordCharacterEncoding'' or ''characterEncoding''.

MysqlNativePasswordPlugin.1=Unsupported character encoding ''{0}'' for ''passwordCharacterEncoding'' or ''characterEncoding''.

Sha256PasswordPlugin.0=Unable to read public key {0}
Sha256PasswordPlugin.1=Unable to close public key file
Sha256PasswordPlugin.2=Public Key Retrieval is not allowed
Sha256PasswordPlugin.3=Unsupported character encoding ''{0}'' for ''passwordCharacterEncoding'' or ''characterEncoding''.

MysqlXAConnection.001=Invalid flag, must use TMNOFLAGS, or any combination of TMSTARTRSCAN and TMENDRSCAN 
MysqlXAConnection.002=Error while recovering XIDs from RM. GTRID and BQUAL are wrong sizes
MysqlXAConnection.003=Undetermined error occurred in the underlying Connection - check your data for consistency

SocketMetadata.0=Using 'host' value of ''{0}'' to determine locality of connection
SocketMetadata.1=Locally connected - HostAddress({0}).equals(whereIconnectedTo({1})
SocketMetadata.2=Attempted locally connected check failed - ! HostAddress({0}).equals(whereIconnectedTo({1})
SocketMetadata.3=Remote socket address {0} is not an inet socket address

#
# ConnectionProperty Categories
#

ConnectionProperties.categoryConnectionAuthentication=Connection/Authentication
ConnectionProperties.categoryNetworking=Networking
ConnectionProperties.categoryDebuggingProfiling=Debugging/Profiling
ConnectionProperties.categorryHA=High Availability and Clustering
ConnectionProperties.categoryMisc=Miscellaneous
ConnectionProperties.categoryPerformance=Performance Extensions
ConnectionProperties.categorySecurity=Security

#
# ConnectionProperty Descriptions
#

ConnectionProperties.loadDataLocal=Should the driver allow use of 'LOAD DATA LOCAL INFILE...'?
ConnectionProperties.replicationEnableJMX=Enables JMX-based management of replication connection groups, including live slave promotion, addition of new slaves and removal of master or slave hosts from load-balanced master and slave connection pools.
ConnectionProperties.replicationConnectionGroup=Logical group of replication connections within a classloader, used to manage different groups independently. If not specified, live management of replication connections is disabled.
ConnectionProperties.allowMasterDownConnections=By default, a replication-aware connection will fail to connect when configured master hosts are all unavailable at initial connection. Setting this property to 'true' allows to establish the initial connection, by failing over to the slave servers, in read-only state. It won't prevent subsequent failures when switching back to the master hosts i.e. by setting the replication connection to read/write state.
ConnectionProperties.allowSlaveDownConnections=By default, a replication-aware connection will fail to connect when configured slave hosts are all unavailable at initial connection. Setting this property to 'true' allows to establish the initial connection. It won't prevent failures when switching to slaves i.e. by setting the replication connection to read-only state. The property 'readFromMasterWhenNoSlaves' should be used for this purpose. 
ConnectionProperties.readFromMasterWhenNoSlaves=Replication-aware connections distribute load by using the master hosts when in read/write state and by using the slave hosts when in read-only state. If, when setting the connection to read-only state, none of the slave hosts are available, an SQLExeception is thrown back. Setting this property to 'true' allows to fail over to the master hosts, while setting the connection state to read-only, when no slave hosts are available at switch instant.
ConnectionProperties.allowMultiQueries=Allow the use of ';' to delimit multiple queries during one statement (true/false), defaults to 'false', and does not affect the addBatch() and executeBatch() methods, which instead rely on rewriteBatchedStatements.
ConnectionProperties.allowNANandINF=Should the driver allow NaN or +/- INF values in PreparedStatement.setDouble()?
ConnectionProperties.allowUrlInLoadLocal=Should the driver allow URLs in 'LOAD DATA LOCAL INFILE' statements?
ConnectionProperties.alwaysSendSetIsolation=Should the driver always communicate with the database when Connection.setTransactionIsolation() is called? If set to false, the driver will only communicate with the database when the requested transaction isolation is different than the whichever is newer, the last value that was set via Connection.setTransactionIsolation(), or the value that was read from the server when the connection was established.  Note that useLocalSessionState=true will force the same behavior as alwaysSendSetIsolation=false, regardless of how alwaysSendSetIsolation is set.
ConnectionProperties.autoClosePstmtStreams=Should the driver automatically call .close() on streams/readers passed as arguments via set*() methods?
ConnectionProperties.autoDeserialize=Should the driver automatically detect and de-serialize objects stored in BLOB fields?
ConnectionProperties.autoGenerateTestcaseScript=Should the driver dump the SQL it is executing, including server-side prepared statements to STDERR?
ConnectionProperties.autoReconnect=Should the driver try to re-establish stale and/or dead connections? If enabled the driver will throw an exception for a queries issued on a stale or dead connection, which belong to the current transaction, but will attempt reconnect before the next query issued on the connection in a new transaction. The use of this feature is not recommended, because it has side effects related to session state and data consistency when applications don't handle SQLExceptions properly, and is only designed to be used when you are unable to configure your application to handle SQLExceptions resulting from dead and stale connections properly. Alternatively, as a last option, investigate setting the MySQL server variable "wait_timeout" to a high value, rather than the default of 8 hours.
ConnectionProperties.autoReconnectForPools=Use a reconnection strategy appropriate for connection pools (defaults to 'false')
ConnectionProperties.autoSlowLog=Instead of using slowQueryThreshold* to determine if a query is slow enough to be logged, maintain statistics that allow the driver to determine queries that are outside the 99th percentile?
ConnectionProperties.blobsAreStrings=Should the driver always treat BLOBs as Strings - specifically to work around dubious metadata returned by the server for GROUP BY clauses?
ConnectionProperties.functionsNeverReturnBlobs=Should the driver always treat data from functions returning BLOBs as Strings - specifically to work around dubious metadata returned by the server for GROUP BY clauses?
ConnectionProperties.blobSendChunkSize=Chunk size to use when sending BLOB/CLOBs via ServerPreparedStatements. Note that this value cannot exceed the value of "maxAllowedPacket" and, if that is the case, then this value will be corrected automatically.
ConnectionProperties.cacheCallableStatements=Should the driver cache the parsing stage of CallableStatements
ConnectionProperties.cachePrepStmts=Should the driver cache the parsing stage of PreparedStatements of client-side prepared statements, the "check" for suitability of server-side prepared and server-side prepared statements themselves?
ConnectionProperties.cacheRSMetadata=Should the driver cache ResultSetMetaData for Statements and PreparedStatements? (Req. JDK-1.4+, true/false, default 'false')
ConnectionProperties.cacheServerConfiguration=Should the driver cache the results of 'SHOW VARIABLES' and 'SHOW COLLATION' on a per-URL basis?
ConnectionProperties.callableStmtCacheSize=If 'cacheCallableStmts' is enabled, how many callable statements should be cached?
ConnectionProperties.capitalizeTypeNames=Capitalize type names in DatabaseMetaData? (usually only useful when using WebObjects, true/false, defaults to 'false')
ConnectionProperties.characterEncoding=If 'useUnicode' is set to true, what character encoding should the driver use when dealing with strings? (defaults is to 'autodetect')
ConnectionProperties.characterSetResults=Character set to tell the server to return results as.
ConnectionProperties.clientInfoProvider=The name of a class that implements the com.mysql.jdbc.JDBC4ClientInfoProvider interface in order to support JDBC-4.0's Connection.get/setClientInfo() methods
ConnectionProperties.clobberStreamingResults=This will cause a 'streaming' ResultSet to be automatically closed, and any outstanding data still streaming from the server to be discarded if another query is executed before all the data has been read from the server.
ConnectionProperties.clobCharacterEncoding=The character encoding to use for sending and retrieving TEXT, MEDIUMTEXT and LONGTEXT values instead of the configured connection characterEncoding
ConnectionProperties.compensateOnDuplicateKeyUpdateCounts=Should the driver compensate for the update counts of "ON DUPLICATE KEY" INSERT statements (2 = 1, 0 = 1) when using prepared statements?
ConnectionProperties.connectionCollation=If set, tells the server to use this collation in SET NAMES charset COLLATE connectionCollation. Also overrides the characterEncoding with those corresponding to character set of this collation.
ConnectionProperties.connectionLifecycleInterceptors=A comma-delimited list of classes that implement "com.mysql.jdbc.ConnectionLifecycleInterceptor" that should notified of connection lifecycle events (creation, destruction, commit, rollback, setCatalog and setAutoCommit) and potentially alter the execution of these commands. ConnectionLifecycleInterceptors are "stackable", more than one interceptor may be specified via the configuration property as a comma-delimited list, with the interceptors executed in order from left to right.
ConnectionProperties.connectTimeout=Timeout for socket connect (in milliseconds), with 0 being no timeout. Only works on JDK-1.4 or newer. Defaults to '0'.
ConnectionProperties.continueBatchOnError=Should the driver continue processing batch commands if one statement fails. The JDBC spec allows either way (defaults to 'true').
ConnectionProperties.createDatabaseIfNotExist=Creates the database given in the URL if it doesn't yet exist. Assumes the configured user has permissions to create databases.
ConnectionProperties.defaultFetchSize=The driver will call setFetchSize(n) with this value on all newly-created Statements
ConnectionProperties.useServerPrepStmts=Use server-side prepared statements if the server supports them?
ConnectionProperties.dontTrackOpenResources=The JDBC specification requires the driver to automatically track and close resources, however if your application doesn't do a good job of explicitly calling close() on statements or result sets, this can cause memory leakage. Setting this property to true relaxes this constraint, and can be more memory efficient for some applications. Also the automatic closing of the Statement and current ResultSet in Statement.closeOnCompletion() and Statement.getMoreResults ([Statement.CLOSE_CURRENT_RESULT | Statement.CLOSE_ALL_RESULTS]), respectively, ceases to happen. This property automatically sets holdResultsOpenOverStatementClose=true.
ConnectionProperties.dumpQueriesOnException=Should the driver dump the contents of the query sent to the server in the message for SQLExceptions?
ConnectionProperties.dynamicCalendars=Should the driver retrieve the default calendar when required, or cache it per connection/session?
ConnectionProperties.eliseSetAutoCommit=If using MySQL-4.1 or newer, should the driver only issue 'set autocommit=n' queries when the server's state doesn't match the requested state by Connection.setAutoCommit(boolean)?
ConnectionProperties.emptyStringsConvertToZero=Should the driver allow conversions from empty string fields to numeric values of '0'?
ConnectionProperties.emulateLocators=Should the driver emulate java.sql.Blobs with locators? With this feature enabled, the driver will delay loading the actual Blob data until the one of the retrieval methods (getInputStream(), getBytes(), and so forth) on the blob data stream has been accessed. For this to work, you must use a column alias with the value of the column to the actual name of the Blob. The feature also has the following restrictions: The SELECT that created the result set must reference only one table, the table must have a primary key; the SELECT must alias the original blob column name, specified as a string, to an alternate name; the SELECT must cover all columns that make up the primary key. 
ConnectionProperties.emulateUnsupportedPstmts=Should the driver detect prepared statements that are not supported by the server, and replace them with client-side emulated versions?
ConnectionProperties.enablePacketDebug=When enabled, a ring-buffer of 'packetDebugBufferSize' packets will be kept, and dumped when exceptions are thrown in key areas in the driver's code
ConnectionProperties.enableQueryTimeouts=When enabled, query timeouts set via Statement.setQueryTimeout() use a shared java.util.Timer instance for scheduling. Even if the timeout doesn't expire before the query is processed, there will be memory used by the TimerTask for the given timeout which won't be reclaimed until the time the timeout would have expired if it hadn't been cancelled by the driver. High-load environments might want to consider disabling this functionality.
ConnectionProperties.explainSlowQueries=If 'logSlowQueries' is enabled, should the driver automatically issue an 'EXPLAIN' on the server and send the results to the configured logger at a WARN level?
ConnectionProperties.failoverReadOnly=When failing over in autoReconnect mode, should the connection be set to 'read-only'?
ConnectionProperties.gatherPerfMetrics=Should the driver gather performance metrics, and report them via the configured logger every 'reportMetricsIntervalMillis' milliseconds?
ConnectionProperties.generateSimpleParameterMetadata=Should the driver generate simplified parameter metadata for PreparedStatements when no metadata is available either because the server couldn't support preparing the statement, or server-side prepared statements are disabled?
ConnectionProperties.holdRSOpenOverStmtClose=Should the driver close result sets on Statement.close() as required by the JDBC specification?
ConnectionProperties.ignoreNonTxTables=Ignore non-transactional table warning for rollback? (defaults to 'false').
ConnectionProperties.includeInnodbStatusInDeadlockExceptions=Include the output of "SHOW ENGINE INNODB STATUS" in exception messages when deadlock exceptions are detected?
ConnectionProperties.includeThreadDumpInDeadlockExceptions=Include a current Java thread dump in exception messages when deadlock exceptions are detected?
ConnectionProperties.includeThreadNamesAsStatementComment=Include the name of the current thread as a comment visible in "SHOW PROCESSLIST", or in Innodb deadlock dumps, useful in correlation with "includeInnodbStatusInDeadlockExceptions=true" and "includeThreadDumpInDeadlockExceptions=true". 
ConnectionProperties.initialTimeout=If autoReconnect is enabled, the initial time to wait between re-connect attempts (in seconds, defaults to '2').
ConnectionProperties.interactiveClient=Set the CLIENT_INTERACTIVE flag, which tells MySQL to timeout connections based on INTERACTIVE_TIMEOUT instead of WAIT_TIMEOUT
ConnectionProperties.jdbcCompliantTruncation=Should the driver throw java.sql.DataTruncation exceptions when data is truncated as is required by the JDBC specification when connected to a server that supports warnings (MySQL 4.1.0 and newer)? This property has no effect if the server sql-mode includes STRICT_TRANS_TABLES.
ConnectionProperties.largeRowSizeThreshold=What size result set row should the JDBC driver consider "large", and thus use a more memory-efficient way of representing the row internally?
ConnectionProperties.loadBalanceStrategy=If using a load-balanced connection to connect to SQL nodes in a MySQL Cluster/NDB configuration (by using the URL prefix "**********************://"), which load balancing algorithm should the driver use: (1) "random" - the driver will pick a random host for each request. This tends to work better than round-robin, as the randomness will somewhat account for spreading loads where requests vary in response time, while round-robin can sometimes lead to overloaded nodes if there are variations in response times across the workload. (2) "bestResponseTime" - the driver will route the request to the host that had the best response time for the previous transaction. (3) "serverAffinity" - the driver initially attempts to enforce server affinity while still respecting and benefiting from the fault tolerance aspects of the load-balancing implementation. The server affinity ordered list is provided using the property 'serverAffinityOrder'. If none of the servers listed in the affinity list is responsive, the driver then refers to the "random" strategy to proceed with choosing the next server.
ConnectionProperties.serverAffinityOrder=A comma separated list containing the host/port pairs that are to be used in load-balancing "serverAffinity" strategy. Only the sub-set of the hosts enumerated in the main hosts section in this URL will be used and they must be identical in case and type, i.e., can't use an IP address in one place and the corresponding host name in the other. 
ConnectionProperties.loadBalanceBlacklistTimeout=Time in milliseconds between checks of servers which are unavailable, by controlling how long a server lives in the global blacklist.
ConnectionProperties.loadBalancePingTimeout=Time in milliseconds to wait for ping response from each of load-balanced physical connections when using load-balanced Connection.
ConnectionProperties.loadBalanceValidateConnectionOnSwapServer=Should the load-balanced Connection explicitly check whether the connection is live when swapping to a new physical connection at commit/rollback?
ConnectionProperties.loadBalanceConnectionGroup=Logical group of load-balanced connections within a classloader, used to manage different groups independently. If not specified, live management of load-balanced connections is disabled.
ConnectionProperties.loadBalanceExceptionChecker=Fully-qualified class name of custom exception checker.  The class must implement com.mysql.jdbc.LoadBalanceExceptionChecker interface, and is used to inspect SQLExceptions and determine whether they should trigger fail-over to another host in a load-balanced deployment.
ConnectionProperties.loadBalanceSQLStateFailover=Comma-delimited list of SQLState codes used by default load-balanced exception checker to determine whether a given SQLException should trigger failover.  The SQLState of a given SQLException is evaluated to determine whether it begins with any value in the comma-delimited list.
ConnectionProperties.loadBalanceSQLExceptionSubclassFailover=Comma-delimited list of classes/interfaces used by default load-balanced exception checker to determine whether a given SQLException should trigger failover.  The comparison is done using Class.isInstance(SQLException) using the thrown SQLException.
ConnectionProperties.loadBalanceEnableJMX=Enables JMX-based management of load-balanced connection groups, including live addition/removal of hosts from load-balancing pool.
ConnectionProperties.loadBalanceHostRemovalGracePeriod=Sets the grace period to wait for a host being removed from a load-balanced connection, to be released when it is currently the active host.
ConnectionProperties.loadBalanceAutoCommitStatementThreshold=When auto-commit is enabled, the number of statements which should be executed before triggering load-balancing to rebalance.  Default value of 0 causes load-balanced connections to only rebalance when exceptions are encountered, or auto-commit is disabled and transactions are explicitly committed or rolled back.
ConnectionProperties.loadBalanceAutoCommitStatementRegex=When load-balancing is enabled for auto-commit statements (via loadBalanceAutoCommitStatementThreshold), the statement counter will only increment when the SQL matches the regular expression.  By default, every statement issued matches.
ConnectionProperties.localSocketAddress=Hostname or IP address given to explicitly configure the interface that the driver will bind the client side of the TCP/IP connection to when connecting.
ConnectionProperties.locatorFetchBufferSize=If 'emulateLocators' is configured to 'true', what size buffer should be used when fetching BLOB data for getBinaryInputStream?
ConnectionProperties.logger=The name of a class that implements \"{0}\"  that will be used to log messages to. (default is \"{1}\", which logs to STDERR)
ConnectionProperties.logSlowQueries=Should queries that take longer than 'slowQueryThresholdMillis' or detected by the 'autoSlowLog' monitoring be reported to the registered 'profilerEventHandler'?
ConnectionProperties.logXaCommands=Should the driver log XA commands sent by MysqlXaConnection to the server, at the DEBUG level of logging?
ConnectionProperties.maintainTimeStats=Should the driver maintain various internal timers to enable idle time calculations as well as more verbose error messages when the connection to the server fails? Setting this property to false removes at least two calls to System.getCurrentTimeMillis() per query.
ConnectionProperties.maxQuerySizeToLog=Controls the maximum length of the part of a query that will get logged when profiling or tracing
ConnectionProperties.maxReconnects=Maximum number of reconnects to attempt if autoReconnect is true, default is '3'.
ConnectionProperties.maxRows=The maximum number of rows to return (0, the default means return all rows).
ConnectionProperties.allVersions=all versions
ConnectionProperties.metadataCacheSize=The number of queries to cache ResultSetMetadata for if cacheResultSetMetaData is set to 'true' (default 50)
ConnectionProperties.netTimeoutForStreamingResults=What value should the driver automatically set the server setting 'net_write_timeout' to when the streaming result sets feature is in use? (value has unit of seconds, the value '0' means the driver will not try and adjust this value)
ConnectionProperties.noAccessToProcedureBodies=When determining procedure parameter types for CallableStatements, and the connected user can't access procedure bodies through "SHOW CREATE PROCEDURE" or select on mysql.proc should the driver instead create basic metadata (all parameters reported as INOUT VARCHARs) instead of throwing an exception?
ConnectionProperties.noDatetimeStringSync=Don't ensure that ResultSet.getDatetimeType().toString().equals(ResultSet.getString())
ConnectionProperties.noTzConversionForTimeType=Don't convert TIME values using the server time zone if 'useTimezone'='true'
ConnectionProperties.noTzConversionForDateType=Don't convert DATE values using the server time zone if 'useTimezone'='true' or 'useLegacyDatetimeCode'='false'
ConnectionProperties.cacheDefaultTimezone=Caches client's default time zone. This results in better performance when dealing with time zone conversions in Date and Time data types, however it won't be aware of time zone changes if they happen at runtime.
ConnectionProperties.nullCatalogMeansCurrent=When DatabaseMetadataMethods ask for a 'catalog' parameter, does the value null mean use the current catalog? (this is not JDBC-compliant, but follows legacy behavior from earlier versions of the driver)
ConnectionProperties.nullNamePatternMatchesAll=Should DatabaseMetaData methods that accept *pattern parameters treat null the same as '%' (this is not JDBC-compliant, however older versions of the driver accepted this departure from the specification)
ConnectionProperties.packetDebugBufferSize=The maximum number of packets to retain when 'enablePacketDebug' is true
ConnectionProperties.padCharsWithSpace=If a result set column has the CHAR type and the value does not fill the amount of characters specified in the DDL for the column, should the driver pad the remaining characters with space (for ANSI compliance)?
ConnectionProperties.paranoid=Take measures to prevent exposure sensitive information in error messages and clear data structures holding sensitive data when possible? (defaults to 'false')
ConnectionProperties.pedantic=Follow the JDBC spec to the letter.
ConnectionProperties.pinGlobalTxToPhysicalConnection=When using XAConnections, should the driver ensure that operations on a given XID are always routed to the same physical connection? This allows the XAConnection to support "XA START ... JOIN" after "XA END" has been called
ConnectionProperties.populateInsertRowWithDefaultValues=When using ResultSets that are CONCUR_UPDATABLE, should the driver pre-populate the "insert" row with default values from the DDL for the table used in the query so those values are immediately available for ResultSet accessors? This functionality requires a call to the database for metadata each time a result set of this type is created. If disabled (the default), the default values will be populated by the an internal call to refreshRow() which pulls back default values and/or values changed by triggers.
ConnectionProperties.prepStmtCacheSize=If prepared statement caching is enabled, how many prepared statements should be cached?
ConnectionProperties.prepStmtCacheSqlLimit=If prepared statement caching is enabled, what's the largest SQL the driver will cache the parsing for?
ConnectionProperties.processEscapeCodesForPrepStmts=Should the driver process escape codes in queries that are prepared? Default escape processing behavior in non-prepared statements must be defined with the property 'enableEscapeProcessing'.
ConnectionProperties.profilerEventHandler=Name of a class that implements the interface com.mysql.jdbc.profiler.ProfilerEventHandler that will be used to handle profiling/tracing events.
ConnectionProperties.profileSqlDeprecated=Deprecated, use 'profileSQL' instead. Trace queries and their execution/fetch times on STDERR (true/false) defaults to 'false'
ConnectionProperties.profileSQL=Trace queries and their execution/fetch times to the configured 'profilerEventHandler'
ConnectionProperties.connectionPropertiesTransform=An implementation of com.mysql.jdbc.ConnectionPropertiesTransform that the driver will use to modify URL properties passed to the driver before attempting a connection
ConnectionProperties.queriesBeforeRetryMaster=Number of queries to issue before falling back to the primary host when failed over (when using multi-host failover). Whichever condition is met first, 'queriesBeforeRetryMaster' or 'secondsBeforeRetryMaster' will cause an attempt to be made to reconnect to the primary host. Setting both properties to 0 disables the automatic fall back to the primary host at transaction boundaries. Defaults to 50.
ConnectionProperties.reconnectAtTxEnd=If autoReconnect is set to true, should the driver attempt reconnections at the end of every transaction?
ConnectionProperties.relaxAutoCommit=If the version of MySQL the driver connects to does not support transactions, still allow calls to commit(), rollback() and setAutoCommit() (true/false, defaults to 'false')?
ConnectionProperties.reportMetricsIntervalMillis=If 'gatherPerfMetrics' is enabled, how often should they be logged (in ms)?
ConnectionProperties.requireSSL=Require server support of SSL connection if useSSL=true? (defaults to 'false').
ConnectionProperties.resourceId=A globally unique name that identifies the resource that this datasource or connection is connected to, used for XAResource.isSameRM() when the driver can't determine this value based on hostnames used in the URL
ConnectionProperties.resultSetSizeThreshold=If 'useUsageAdvisor' is true, how many rows should a result set contain before the driver warns that it is suspiciously large?
ConnectionProperties.retainStatementAfterResultSetClose=Should the driver retain the Statement reference in a ResultSet after ResultSet.close() has been called. This is not JDBC-compliant after JDBC-4.0.
ConnectionProperties.retriesAllDown=When using loadbalancing or failover, the number of times the driver should cycle through available hosts, attempting to connect.  Between cycles, the driver will pause for 250ms if no servers are available.
ConnectionProperties.rewriteBatchedStatements=Should the driver use multiqueries (irregardless of the setting of "allowMultiQueries") as well as rewriting of prepared statements for INSERT into multi-value inserts when executeBatch() is called? Notice that this has the potential for SQL injection if using plain java.sql.Statements and your code doesn't sanitize input correctly. Notice that for prepared statements, if you don't specify stream lengths when using PreparedStatement.set*Stream(), the driver won't be able to determine the optimum number of parameters per batch and you might receive an error from the driver that the resultant packet is too large. Statement.getGeneratedKeys() for these rewritten statements only works when the entire batch includes INSERT statements. Please be aware using rewriteBatchedStatements=true with INSERT .. ON DUPLICATE KEY UPDATE that for rewritten statement server returns only one value as sum of all affected (or found) rows in batch and it isn't possible to map it correctly to initial statements; in this case driver returns 0 as a result of each batch statement if total count was 0, and the Statement.SUCCESS_NO_INFO as a result of each batch statement if total count was > 0.
ConnectionProperties.rollbackOnPooledClose=Should the driver issue a rollback() when the logical connection in a pool is closed?
ConnectionProperties.roundRobinLoadBalance=When autoReconnect is enabled, and failoverReadonly is false, should we pick hosts to connect to on a round-robin basis?
ConnectionProperties.runningCTS13=Enables workarounds for bugs in Sun's JDBC compliance testsuite version 1.3
ConnectionProperties.secondsBeforeRetryMaster=How long should the driver wait, when failed over, before attempting to reconnect to the primary host? Whichever condition is met first, 'queriesBeforeRetryMaster' or 'secondsBeforeRetryMaster' will cause an attempt to be made to reconnect to the master. Setting both properties to 0 disables the automatic fall back to the primary host at transaction boundaries. Time in seconds, defaults to 30
ConnectionProperties.selfDestructOnPingSecondsLifetime=If set to a non-zero value, the driver will close the connection and report failure when Connection.ping() or Connection.isValid(int) is called if the connection's lifetime exceeds this value (in milliseconds).
ConnectionProperties.selfDestructOnPingMaxOperations=If set to a non-zero value, the driver will report close the connection and report failure when Connection.ping() or Connection.isValid(int) is called if the connection's count of commands sent to the server exceeds this value.
ConnectionProperties.serverTimezone=Override detection/mapping of time zone. Used when time zone from server doesn't map to Java time zone
ConnectionProperties.sessionVariables=A comma or semicolon separated list of name=value pairs to be sent as SET [SESSION] ... to the server when the driver connects.
ConnectionProperties.slowQueryThresholdMillis=If 'logSlowQueries' is enabled, how long should a query take (in ms) before it is logged as slow?
ConnectionProperties.slowQueryThresholdNanos=If 'logSlowQueries' is enabled, 'useNanosForElapsedTime' is set to true, and this property is set to a non-zero value, the driver will use this threshold (in nanosecond units) to determine if a query was slow.
ConnectionProperties.socketFactory=The name of the class that the driver should use for creating socket connections to the server. This class must implement the interface 'com.mysql.jdbc.SocketFactory' and have public no-args constructor.
ConnectionProperties.socketTimeout=Timeout (in milliseconds) on network socket operations (0, the default means no timeout).
ConnectionProperties.socksProxyHost=Name or IP address of SOCKS host to connect through.
ConnectionProperties.socksProxyPort=Port of SOCKS server.
ConnectionProperties.statementInterceptors=A comma-delimited list of classes that implement "com.mysql.jdbc.StatementInterceptor" that should be placed "in between" query execution to influence the results. StatementInterceptors are "chainable", the results returned by the "current" interceptor will be passed on to the next in in the chain, from left-to-right order, as specified in this property. 
ConnectionProperties.strictFloatingPoint=Used only in older versions of compliance test
ConnectionProperties.strictUpdates=Should the driver do strict checking (all primary keys selected) of updatable result sets (true, false, defaults to 'true')?
ConnectionProperties.overrideSupportsIEF=Should the driver return "true" for DatabaseMetaData.supportsIntegrityEnhancementFacility() even if the database doesn't support it to workaround applications that require this method to return "true" to signal support of foreign keys, even though the SQL specification states that this facility contains much more than just foreign key support (one such application being OpenOffice)?
ConnectionProperties.tcpNoDelay=If connecting using TCP/IP, should the driver set SO_TCP_NODELAY (disabling the Nagle Algorithm)?
ConnectionProperties.tcpKeepAlive=If connecting using TCP/IP, should the driver set SO_KEEPALIVE?
ConnectionProperties.tcpSoRcvBuf=If connecting using TCP/IP, should the driver set SO_RCV_BUF to the given value? The default value of '0', means use the platform default value for this property)
ConnectionProperties.tcpSoSndBuf=If connecting using TCP/IP, should the driver set SO_SND_BUF to the given value? The default value of '0', means use the platform default value for this property)
ConnectionProperties.tcpTrafficClass=If connecting using TCP/IP, should the driver set traffic class or type-of-service fields ?See the documentation for java.net.Socket.setTrafficClass() for more information.
ConnectionProperties.tinyInt1isBit=Should the driver treat the datatype TINYINT(1) as the BIT type (because the server silently converts BIT -> TINYINT(1) when creating tables)?
ConnectionProperties.traceProtocol=Should the network protocol be logged at the TRACE level?
ConnectionProperties.treatUtilDateAsTimestamp=Should the driver treat java.util.Date as a TIMESTAMP for the purposes of PreparedStatement.setObject()?
ConnectionProperties.transformedBitIsBoolean=If the driver converts TINYINT(1) to a different type, should it use BOOLEAN instead of BIT for future compatibility with MySQL-5.0, as MySQL-5.0 has a BIT type?
ConnectionProperties.useCompression=Use zlib compression when communicating with the server (true/false)? Defaults to 'false'.
ConnectionProperties.useConfigs=Load the comma-delimited list of configuration properties before parsing the URL or applying user-specified properties. These configurations are explained in the 'Configurations' of the documentation.
ConnectionProperties.useCursorFetch=If connected to MySQL > 5.0.2, and setFetchSize() > 0 on a statement, should that statement use cursor-based fetching to retrieve rows?
ConnectionProperties.useDynamicCharsetInfo=Should the driver use a per-connection cache of character set information queried from the server when necessary, or use a built-in static mapping that is more efficient, but isn't aware of custom character sets or character sets implemented after the release of the JDBC driver?
ConnectionProperties.useFastIntParsing=Use internal String->Integer conversion routines to avoid excessive object creation?
ConnectionProperties.useFastDateParsing=Use internal String->Date/Time/Timestamp conversion routines to avoid excessive object creation? This is part of the legacy date-time code, thus the property has an effect only when "useLegacyDatetimeCode=true."
ConnectionProperties.useHostsInPrivileges=Add '@hostname' to users in DatabaseMetaData.getColumn/TablePrivileges() (true/false), defaults to 'true'.
ConnectionProperties.useInformationSchema=When connected to MySQL-5.0.7 or newer, should the driver use the INFORMATION_SCHEMA to derive information used by DatabaseMetaData?
ConnectionProperties.useJDBCCompliantTimezoneShift=Should the driver use JDBC-compliant rules when converting TIME/TIMESTAMP/DATETIME values' time zone information for those JDBC arguments which take a java.util.Calendar argument? This is part of the legacy date-time code, thus the property has an effect only when "useLegacyDatetimeCode=true."
ConnectionProperties.useLocalSessionState=Should the driver refer to the internal values of autocommit and transaction isolation that are set by Connection.setAutoCommit() and Connection.setTransactionIsolation() and transaction state as maintained by the protocol, rather than querying the database or blindly sending commands to the database for commit() or rollback() method calls?
ConnectionProperties.useLocalTransactionState=Should the driver use the in-transaction state provided by the MySQL protocol to determine if a commit() or rollback() should actually be sent to the database?
ConnectionProperties.useNanosForElapsedTime=For profiling/debugging functionality that measures elapsed time, should the driver try to use nanoseconds resolution if available (JDK >= 1.5)?
ConnectionProperties.useOldAliasMetadataBehavior=Should the driver use the legacy behavior for "AS" clauses on columns and tables, and only return aliases (if any) for ResultSetMetaData.getColumnName() or ResultSetMetaData.getTableName() rather than the original column/table name? In 5.0.x, the default value was true.
ConnectionProperties.useOldUtf8Behavior=Use the UTF-8 behavior the driver did when communicating with 4.0 and older servers
ConnectionProperties.useOnlyServerErrorMessages=Don't prepend 'standard' SQLState error messages to error messages returned by the server.
ConnectionProperties.useReadAheadInput=Use newer, optimized non-blocking, buffered input stream when reading from the server?
ConnectionProperties.useSqlStateCodes=Use SQL Standard state codes instead of 'legacy' X/Open/SQL state codes (true/false), default is 'true'
ConnectionProperties.useSSL=Use SSL when communicating with the server (true/false), default is 'true' when connecting to MySQL 5.5.45+, 5.6.26+ or 5.7.6+, otherwise default is 'false'
ConnectionProperties.useSSPSCompatibleTimezoneShift=If migrating from an environment that was using server-side prepared statements, and the configuration property "useJDBCCompliantTimeZoneShift" set to "true", use compatible behavior when not using server-side prepared statements when sending TIMESTAMP values to the MySQL server.
ConnectionProperties.useStreamLengthsInPrepStmts=Honor stream length parameter in PreparedStatement/ResultSet.setXXXStream() method calls (true/false, defaults to 'true')?
ConnectionProperties.useTimezone=Convert time/date types between client and server time zones (true/false, defaults to 'false')? This is part of the legacy date-time code, thus the property has an effect only when "useLegacyDatetimeCode=true."
ConnectionProperties.ultraDevHack=Create PreparedStatements for prepareCall() when required, because UltraDev is broken and issues a prepareCall() for _all_ statements? (true/false, defaults to 'false')
ConnectionProperties.useUnbufferedInput=Don't use BufferedInputStream for reading data from the server
ConnectionProperties.useUnicode=Should the driver use Unicode character encodings when handling strings? Should only be used when the driver can't determine the character set mapping, or you are trying to 'force' the driver to use a character set that MySQL either doesn't natively support (such as UTF-8), true/false, defaults to 'true'
ConnectionProperties.useUsageAdvisor=Should the driver issue 'usage' warnings advising proper and efficient usage of JDBC and MySQL Connector/J to the 'profilerEventHandler'?
ConnectionProperties.verifyServerCertificate=If "useSSL" is set to "true", should the driver verify the server's certificate? When using this feature, the keystore parameters should be specified by the "clientCertificateKeyStore*" properties, rather than system properties. Default is 'false' when connecting to MySQL 5.5.45+, 5.6.26+ or 5.7.6+ and "useSSL" was not explicitly set to "true". Otherwise default is 'true'
ConnectionProperties.yearIsDateType=Should the JDBC driver treat the MySQL type "YEAR" as a java.sql.Date, or as a SHORT?
ConnectionProperties.zeroDateTimeBehavior=What should happen when the driver encounters DATETIME values that are composed entirely of zeros (used by MySQL to represent invalid dates)? Valid values are \"{0}\", \"{1}\" and \"{2}\".
ConnectionProperties.useJvmCharsetConverters=Always use the character encoding routines built into the JVM, rather than using lookup tables for single-byte character sets?
ConnectionProperties.useGmtMillisForDatetimes=Convert between session time zone and GMT before creating Date and Timestamp instances (value of 'false' leads to legacy behavior, 'true' leads to more JDBC-compliant behavior)? This is part of the legacy date-time code, thus the property has an effect only when "useLegacyDatetimeCode=true."
ConnectionProperties.dumpMetadataOnColumnNotFound=Should the driver dump the field-level metadata of a result set into the exception message when ResultSet.findColumn() fails?
ConnectionProperties.clientCertificateKeyStoreUrl=URL to the client certificate KeyStore (if not specified, use defaults)
ConnectionProperties.trustCertificateKeyStoreUrl=URL to the trusted root certificate KeyStore (if not specified, use defaults)
ConnectionProperties.clientCertificateKeyStoreType=KeyStore type for client certificates (NULL or empty means use the default, which is "JKS". Standard keystore types supported by the JVM are "JKS" and "PKCS12", your environment may have more available depending on what security products are installed and available to the JVM.
ConnectionProperties.clientCertificateKeyStorePassword=Password for the client certificates KeyStore
ConnectionProperties.trustCertificateKeyStoreType=KeyStore type for trusted root certificates (NULL or empty means use the default, which is "JKS". Standard keystore types supported by the JVM are "JKS" and "PKCS12", your environment may have more available depending on what security products are installed and available to the JVM.
ConnectionProperties.trustCertificateKeyStorePassword=Password for the trusted root certificates KeyStore
ConnectionProperties.serverRSAPublicKeyFile=File path to the server RSA public key file for sha256_password authentication. If not specified, the public key will be retrieved from the server.
ConnectionProperties.allowPublicKeyRetrieval=Allows special handshake roundtrip to get server RSA public key directly from server.
ConnectionProperties.Username=The user to connect as
ConnectionProperties.Password=The password to use when connecting
ConnectionProperties.useBlobToStoreUTF8OutsideBMP=Tells the driver to treat [MEDIUM/LONG]BLOB columns as [LONG]VARCHAR columns holding text encoded in UTF-8 that has characters outside the BMP (4-byte encodings), which MySQL server can't handle natively.
ConnectionProperties.utf8OutsideBmpExcludedColumnNamePattern=When "useBlobToStoreUTF8OutsideBMP" is set to "true", column names matching the given regex will still be treated as BLOBs unless they match the regex specified for "utf8OutsideBmpIncludedColumnNamePattern". The regex must follow the patterns used for the java.util.regex package.
ConnectionProperties.utf8OutsideBmpIncludedColumnNamePattern=Used to specify exclusion rules to "utf8OutsideBmpExcludedColumnNamePattern". The regex must follow the patterns used for the java.util.regex package.
ConnectionProperties.useLegacyDatetimeCode=Use code for DATE/TIME/DATETIME/TIMESTAMP handling in result sets and statements that consistently handles time zone conversions from client to server and back again, or use the legacy code for these datatypes that has been in the driver for backwards-compatibility? Setting this property to 'false' voids the effects of "useTimezone," "useJDBCCompliantTimezoneShift," "useGmtMillisForDatetimes," and "useFastDateParsing."
ConnectionProperties.sendFractionalSeconds=Send fractional part from TIMESTAMP seconds. If set to false, the nanoseconds value of TIMESTAMP values will be truncated before sending any data to the server. This option applies only to prepared statements, callable statements or updatable result sets.
ConnectionProperties.useColumnNamesInFindColumn=Prior to JDBC-4.0, the JDBC specification had a bug related to what could be given as a "column name" to ResultSet methods like findColumn(), or getters that took a String property. JDBC-4.0 clarified "column name" to mean the label, as given in an "AS" clause and returned by ResultSetMetaData.getColumnLabel(), and if no AS clause, the column name. Setting this property to "true" will give behavior that is congruent to JDBC-3.0 and earlier versions of the JDBC specification, but which because of the specification bug could give unexpected results. This property is preferred over "useOldAliasMetadataBehavior" unless you need the specific behavior that it provides with respect to ResultSetMetadata.
ConnectionProperties.useAffectedRows=Don't set the CLIENT_FOUND_ROWS flag when connecting to the server (not JDBC-compliant, will break most applications that rely on "found" rows vs. "affected rows" for DML statements), but does cause "correct" update counts from "INSERT ... ON DUPLICATE KEY UPDATE" statements to be returned by the server.
ConnectionProperties.passwordCharacterEncoding=What character encoding is used for passwords? Leaving this set to the default value (null), uses the value set in "characterEncoding" if there is one, otherwise uses UTF-8 as default encoding. If the password contains non-ASCII characters, the password encoding must match what server encoding was set to when the password was created. For passwords in other character encodings, the encoding will have to be specified with this property (or with "characterEncoding"), as it's not possible for the driver to auto-detect this.
ConnectionProperties.exceptionInterceptors=Comma-delimited list of classes that implement com.mysql.jdbc.ExceptionInterceptor. These classes will be instantiated one per Connection instance, and all SQLExceptions thrown by the driver will be allowed to be intercepted by these interceptors, in a chained fashion, with the first class listed as the head of the chain.
ConnectionProperties.maxAllowedPacket=Maximum allowed packet size to send to server. If not set, the value of system variable 'max_allowed_packet' will be used to initialize this upon connecting. This value will not take effect if set larger than the value of 'max_allowed_packet'. Also, due to an internal dependency with the property "blobSendChunkSize", this setting has a minimum value of "8203" if "useServerPrepStmts" is set to "true".
ConnectionProperties.queryTimeoutKillsConnection=If the timeout given in Statement.setQueryTimeout() expires, should the driver forcibly abort the Connection instead of attempting to abort the query?
ConnectionProperties.authenticationPlugins=Comma-delimited list of classes that implement com.mysql.jdbc.AuthenticationPlugin and which will be used for authentication unless disabled by "disabledAuthenticationPlugins" property.
ConnectionProperties.disabledAuthenticationPlugins=Comma-delimited list of classes implementing com.mysql.jdbc.AuthenticationPlugin or mechanisms, i.e. "mysql_native_password". The authentication plugins or mechanisms listed will not be used for authentication which will fail if it requires one of them. It is an error to disable the default authentication plugin (either the one named by "defaultAuthenticationPlugin" property or the hard-coded one if "defaultAuthenticationPlugin" property is not set).
ConnectionProperties.defaultAuthenticationPlugin=Name of a class implementing com.mysql.jdbc.AuthenticationPlugin which will be used as the default authentication plugin (see below). It is an error to use a class which is not listed in "authenticationPlugins" nor it is one of the built-in plugins. It is an error to set as default a plugin which was disabled with "disabledAuthenticationPlugins" property. It is an error to set this value to null or the empty string (i.e. there must be at least a valid default authentication plugin specified for the connection, meeting all constraints listed above).
ConnectionProperties.parseInfoCacheFactory=Name of a class implementing com.mysql.jdbc.CacheAdapterFactory, which will be used to create caches for the parsed representation of client-side prepared statements.
ConnectionProperties.serverConfigCacheFactory=Name of a class implementing com.mysql.jdbc.CacheAdapterFactory<String, Map<String, String>>, which will be used to create caches for MySQL server configuration values
ConnectionProperties.disconnectOnExpiredPasswords=If "disconnectOnExpiredPasswords" is set to "false" and password is expired then server enters "sandbox" mode and sends ERR(08001, ER_MUST_CHANGE_PASSWORD) for all commands that are not needed to set a new password until a new password is set.
ConnectionProperties.connectionAttributes=A comma-delimited list of user-defined key:value pairs (in addition to standard MySQL-defined key:value pairs) to be passed to MySQL Server for display as connection attributes in the PERFORMANCE_SCHEMA.SESSION_CONNECT_ATTRS table.  Example usage:  connectionAttributes=key1:value1,key2:value2  This functionality is available for use with MySQL Server version 5.6 or later only.  Earlier versions of MySQL Server do not support connection attributes, causing this configuration option to be ignored.  Setting connectionAttributes=none will cause connection attribute processing to be bypassed, for situations where Connection creation/initialization speed is critical.
ConnectionProperties.getProceduresReturnsFunctions=Pre-JDBC4 DatabaseMetaData API has only the getProcedures() and getProcedureColumns() methods, so they return metadata info for both stored procedures and functions. JDBC4 was extended with the getFunctions() and getFunctionColumns() methods and the expected behaviours of previous methods are not well defined. For JDBC4 and higher, default 'true' value of the option means that calls of DatabaseMetaData.getProcedures() and DatabaseMetaData.getProcedureColumns() return metadata for both procedures and functions as before, keeping backward compatibility. Setting this property to 'false' decouples Connector/J from its pre-JDBC4 behaviours for DatabaseMetaData.getProcedures() and DatabaseMetaData.getProcedureColumns(), forcing them to return metadata for procedures only.
ConnectionProperties.detectCustomCollations=Should the driver detect custom charsets/collations installed on server (true/false, defaults to 'false'). If this option set to 'true' driver gets actual charsets/collations from server each time connection establishes. This could slow down connection initialization significantly.
ConnectionProperties.dontCheckOnDuplicateKeyUpdateInSQL=Stops checking if every INSERT statement contains the "ON DUPLICATE KEY UPDATE" clause. As a side effect, obtaining the statement's generated keys information will return a list where normally it wouldn't. Also be aware that, in this case, the list of generated keys returned may not be accurate. The effect of this property is canceled if set simultaneously with 'rewriteBatchedStatements=true'.
ConnectionProperties.readOnlyPropagatesToServer=Should the driver issue appropriate statements to implicitly set the transaction access mode on server side when Connection.setReadOnly() is called? Setting this property to 'true' enables InnoDB read-only potential optimizations but also requires an extra roundtrip to set the right transaction state. Even if this property is set to 'false', the driver will do its best effort to prevent the execution of database-state-changing queries. Requires minimum of MySQL 5.6.
ConnectionProperties.enabledSSLCipherSuites=If "useSSL" is set to "true", overrides the cipher suites enabled for use on the underlying SSL sockets. This may be required when using external JSSE providers or to specify cipher suites compatible with both MySQL server and used JVM.
ConnectionProperties.enabledTLSProtocols=If "useSSL" is set to "true", overrides the TLS protocols enabled for use on the underlying SSL sockets. This may be used to restrict connections to specific TLS versions.
ConnectionProperties.enableEscapeProcessing=Sets the default escape processing behavior for Statement objects. The method Statement.setEscapeProcessing() can be used to specify the escape processing behavior for an individual Statement object. Default escape processing behavior in prepared statements must be defined with the property 'processEscapeCodesForPrepStmts'.

# 
# Error Messages for Connection Properties
#

ConnectionProperties.unableToInitDriverProperties=Unable to initialize driver properties due to 
ConnectionProperties.unsupportedCharacterEncoding=Unsupported character encoding ''{0}''.
ConnectionProperties.errorNotExpected=Huh?
ConnectionProperties.InternalPropertiesFailure=Internal properties failure
ConnectionProperties.dynamicChangeIsNotAllowed=Dynamic change of ''{0}'' is not allowed.

TimeUtil.UnrecognizedTimezoneId=The server time zone value ''{0}'' is unrecognized or represents more than one time zone. You must \
configure either the server or JDBC driver (via the 'serverTimezone' configuration property) to use a \
more specifc time zone value if you want to utilize time zone support.
TimeUtil.LoadTimeZoneMappingError=Failed to load the time zone mapping resource file 'TimeZoneMapping.properties'.

Connection.exceededConnectionLifetime=Ping or validation failed because configured connection lifetime exceeded.
Connection.badLifecycleInterceptor=Unable to load connection lifecycle interceptor ''{0}''.
MysqlIo.BadStatementInterceptor=Unable to load statement interceptor ''{0}''.
Connection.BadExceptionInterceptor=Unable to load exception interceptor ''{0}''.
Connection.CantDetectLocalConnect=Unable to determine if hostname ''{0}'' is local to this box because of exception, assuming it's not.
Connection.NoMetadataOnSocketFactory=Configured socket factory does not implement SocketMetadata, can not determine whether server is locally-connected, assuming not"
Connection.BadAuthenticationPlugin=Unable to load authentication plugin ''{0}''.
Connection.BadDefaultAuthenticationPlugin=Bad value ''{0}'' for property "defaultAuthenticationPlugin".
Connection.DefaultAuthenticationPluginIsNotListed=defaultAuthenticationPlugin ''{0}'' is not listed in "authenticationPlugins" nor it is one of the built-in plugins.
Connection.BadDisabledAuthenticationPlugin=Can''t disable the default plugin, either remove ''{0}'' from the disabled authentication plugins list, or choose a different default authentication plugin.
Connection.AuthenticationPluginRequiresSSL=SSL connection required for plugin ''{0}''. Check if "useSSL" is set to "true".
Connection.UnexpectedAuthenticationApproval=Unexpected authentication approval: ''{0}'' plugin did not reported "done" state but server has approved connection.
Connection.CantFindCacheFactory=Can not find class ''{0}'' specified by the ''{1}'' configuration property.
Connection.CantLoadCacheFactory=Can not load the cache factory ''{0}'' specified by the ''{1}'' configuration property.
Connection.LoginTimeout=Connection attempt exceeded defined timeout.

LoadBalancedConnectionProxy.badValueForRetriesAllDown=Bad value ''{0}'' for property "retriesAllDown".
LoadBalancedConnectionProxy.badValueForLoadBalanceBlacklistTimeout=Bad value ''{0}'' for property "loadBalanceBlacklistTimeout".
LoadBalancedConnectionProxy.badValueForLoadBalanceHostRemovalGracePeriod=Bad value ''{0}'' for property "loadBalanceHostRemovalGracePeriod".
LoadBalancedConnectionProxy.badValueForLoadBalanceEnableJMX=Bad value ''{0}'' for property "loadBalanceEnableJMX".
LoadBalancedConnectionProxy.badValueForLoadBalanceAutoCommitStatementThreshold=Invalid numeric value ''{0}'' for property "loadBalanceAutoCommitStatementThreshold".
LoadBalancedConnectionProxy.badValueForLoadBalanceAutoCommitStatementRegex=Bad value ''{0}'' for property "loadBalanceAutoCommitStatementRegex".
LoadBalancedConnectionProxy.unusableConnection=The connection is unusable at the current state. There may be no hosts to connect to or all hosts this connection knows may be down at the moment.

ReplicationConnectionProxy.badValueForAllowMasterDownConnections=Bad value ''{0}'' for property "allowMasterDownConnections".
ReplicationConnectionProxy.badValueForReadFromMasterWhenNoSlaves=Bad value ''{0}'' for property "readFromMasterWhenNoSlaves".
ReplicationConnectionProxy.badValueForReplicationEnableJMX=Bad value ''{0}'' for property "replicationEnableJMX".
ReplicationConnectionProxy.initializationWithEmptyHostsLists=A replication connection cannot be initialized without master hosts and slave hosts, simultaneously.
ReplicationConnectionProxy.noHostsInconsistentState=The replication connection is an inconsistent state due to non existing hosts in both its internal hosts lists.
