#
# A configuration that maximizes performance, while
# still staying JDBC-compliant and not doing anything that
# would be "dangerous" to run-of-the-mill J2EE applications
#
# Note that because we're caching things like callable statements
# and the server configuration, this bundle isn't appropriate
# for use with servers that get config'd dynamically without
# restarting the application using this configuration bundle.

cachePrepStmts=true
cacheCallableStmts=true

cacheServerConfiguration=true

#
# Reduces amount of calls to database to set
# session state. "Safe" as long as application uses
# Connection methods to set current database, autocommit
# and transaction isolation
# 

useLocalSessionState=true
elideSetAutoCommits=true
alwaysSendSetIsolation=false

# Can cause high-GC pressure if timeouts are used on every
# query
enableQueryTimeouts=false

# Bypass connection attribute handling during connection
# setup
connectionAttributes=none

