package android.support.p001v4.view;

import android.view.View;

/* renamed from: android.support.v4.view.ViewPropertyAnimatorListenerAdapter */
public class ViewPropertyAnimatorListenerAdapter implements ViewPropertyAnimatorListener {
    @Override // android.support.p001v4.view.ViewPropertyAnimatorListener
    public void onAnimationStart(View view) {
    }

    @Override // android.support.p001v4.view.ViewPropertyAnimatorListener
    public void onAnimationEnd(View view) {
    }

    @Override // android.support.p001v4.view.ViewPropertyAnimatorListener
    public void onAnimationCancel(View view) {
    }
}
