package com.color.home.widgets.database;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.IntentFilter;
import android.graphics.Typeface;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.support.p001v4.internal.view.SupportMenu;
import android.support.p001v4.view.GravityCompat;
import android.text.TextUtils;
import android.util.Log;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TableLayout;
import android.widget.TableRow;
import android.widget.TextView;
import com.color.home.AppController;
import com.color.home.C0213R;
import com.color.home.ProgramParser;
import com.color.home.model.DatabaseInfo;
import com.color.home.network.ExpandNetworkObserver;
import com.color.home.network.NetworkConnectReceiver;
import com.color.home.utils.GraphUtils;
import com.color.home.widgets.OnPlayFinishObserverable;
import com.color.home.widgets.OnPlayFinishedListener;
import com.color.home.widgets.RegionView;
import com.google.gson.Gson;
import java.lang.reflect.Array;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ItemDatabaseView extends LinearLayout implements OnPlayFinishObserverable, Runnable, ExpandNetworkObserver {
    private static final boolean DBG = false;
    private static final boolean DBG_DRAW = false;
    private static final String DELIMITER = "-jm-clt-jm-";
    private static final int NEXT_PAGE = 0;
    private static final String TAG = "ItemDatabaseViewNew";
    private static final int UPDATE_DATA = 1;
    private int LINE_PER_PAGE = 2;
    private boolean isShowTitle = false;
    private String mBorderCellClr;
    private int mBorderCellWidth;
    private String mBorderOuterClr;
    private int mBorderOuterWidth;
    private Context mContext;
    private String[][] mDataArr;
    private int mDataPointer = 0;
    private int mDataRowCount;
    private String mDatabaseInfoStr;
    private int mDbFreshTime;
    private long mDuration = 300000;
    private HandlerThread mHandlerThread;
    private String[] mHeaderArr;
    private boolean mIsMultiPageStarted;
    private boolean mIsShowHeader;
    private boolean mIsStarted = false;
    private ProgramParser.Item mItem;
    private int mLastDataRowCount;
    private String mLastDataString;
    private OnPlayFinishedListener mListener;
    private NetworkConnectReceiver mNetworkConnectReceiver;
    private Handler mQueryHandler;
    private ProgramParser.Region mRegion;
    private Map<String, ProgramParser.SFontFormat> mSFontFormatMap;
    private int mTitleMargin = 10;
    private Handler mUpdateHandler;
    private int mUpdateInterval = 10000;

    public ItemDatabaseView(Context context) {
        super(context);
        this.mContext = context;
        this.mUpdateHandler = new UpdateHandler();
        setOrientation(1);
        this.mSFontFormatMap = new HashMap();
        this.mHandlerThread = new HandlerThread("DatabaseHandlerThread");
        this.mHandlerThread.start();
        initQueryHandler(this.mHandlerThread);
    }

    public void setItem(RegionView regionView, ProgramParser.Item item, ProgramParser.Region region) {
        this.isShowTitle = false;
        this.mListener = regionView;
        this.mItem = item;
        this.mRegion = region;
        initDisplay();
        setBackgroundColor(GraphUtils.parseColor(this.mItem.backcolor));
        setTip(this.mContext.getString(C0213R.string.db_init));
        if (TextUtils.isEmpty(this.mDatabaseInfoStr)) {
            this.mDatabaseInfoStr = new Gson().toJson(generateDatabaseInfo());
        }
        this.mQueryHandler.sendEmptyMessage(1);
        sendHandlerMessage(this.mQueryHandler, 1);
    }

    private void sendHandlerMessage(Handler handler, int msg) {
        if (handler == null) {
            Log.e(TAG, "sendHandlerMessage: handler == null, msg = " + msg);
            return;
        }
        handler.removeMessages(msg);
        handler.sendEmptyMessage(msg);
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void sendHandlerMessageDelayed(Handler handler, int delay, int msg) {
        if (handler == null) {
            Log.e(TAG, "sendHandlerMessageDelayed: handler == null, msg = " + msg);
            return;
        }
        handler.removeMessages(msg);
        handler.sendEmptyMessageDelayed(msg, (long) delay);
    }

    public void showData() {
        int rowCount = DataLab.getInstance().getRowCount(this.mDatabaseInfoStr) - 1;
        Log.i(TAG, "showData: mDataRowCount=" + this.mDataRowCount + ", rowCount=" + rowCount);
        if (rowCount <= 0) {
            this.mDataRowCount = 0;
            showTip();
            Log.e(TAG, "mQueryHandler run: No database data received! mDatabaseInfoStr=" + this.mDatabaseInfoStr + ", mHandler=" + this.mUpdateHandler);
        } else if (!this.mIsStarted || rowCount != this.mDataRowCount) {
            this.mIsStarted = true;
            this.mDataRowCount = rowCount;
            this.mLastDataString = listToString(DataLab.getInstance().getDataList(0, this.mDataRowCount + 1, this.mDatabaseInfoStr));
            synchronized (ItemDatabaseView.class) {
                if (this.mDataRowCount > this.LINE_PER_PAGE) {
                    Log.i(TAG, "showData: handleMultiPage");
                    handleMultiPage(this.mDataRowCount);
                } else {
                    Log.i(TAG, "showData: handleSinglePage");
                    handleSinglePage();
                }
                this.mLastDataRowCount = this.mDataRowCount;
            }
        } else {
            Log.i(TAG, "showData: No change in data length.");
            if (this.mIsMultiPageStarted) {
                Log.i(TAG, "showData: Multi-page display mode, return.");
                return;
            }
            String dataString = listToString(DataLab.getInstance().getDataList(0, this.mDataRowCount + 1, this.mDatabaseInfoStr));
            if (TextUtils.isEmpty(dataString) || dataString.equals(this.mLastDataString)) {
                Log.i(TAG, "showData: Single page data is not updated.");
                return;
            }
            this.mLastDataString = dataString;
            Log.i(TAG, "showData: Single page data updated.");
            showOnePage();
        }
    }

    private String listToString(List<String> list) {
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : list) {
            stringBuilder.append(s);
        }
        return stringBuilder.toString();
    }

    @SuppressLint({"HandlerLeak"})
    private class UpdateHandler extends Handler {
        private UpdateHandler() {
        }

        public void handleMessage(Message msg) {
            if (msg.what != 0) {
                return;
            }
            if (ItemDatabaseView.this.mDataRowCount == 0) {
                Log.e(ItemDatabaseView.TAG, "mUpdateHandler: No data!!!");
                return;
            }
            ItemDatabaseView.this.sendHandlerMessageDelayed(ItemDatabaseView.this.mUpdateHandler, ItemDatabaseView.this.mUpdateInterval, 0);
            try {
                synchronized (ItemDatabaseView.class) {
                    ItemDatabaseView.this.showNextPage();
                }
            } catch (Exception e) {
                Log.e(ItemDatabaseView.TAG, "mUpdateHandler: error.");
                e.printStackTrace();
            }
        }
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void showNextPage() {
        post(new ItemDatabaseView$$Lambda$0(this, this.mDataPointer, getEndIndex()));
    }

    private int getEndIndex() {
        if (this.mDataPointer + this.LINE_PER_PAGE < this.mDataRowCount) {
            int endIdx = this.mDataPointer + this.LINE_PER_PAGE;
            this.mDataPointer += this.LINE_PER_PAGE;
            return endIdx;
        }
        int endIdx2 = this.mDataRowCount;
        this.mDataPointer = 0;
        return endIdx2;
    }

    private void initQueryHandler(HandlerThread handlerThread) {
        if (handlerThread != null) {
            this.mQueryHandler = new Handler(handlerThread.getLooper()) {
                /* class com.color.home.widgets.database.ItemDatabaseView.HandlerC03011 */

                public void handleMessage(Message msg) {
                    if (msg.what == 1) {
                        ItemDatabaseView.this.sendHandlerMessageDelayed(ItemDatabaseView.this.mQueryHandler, ItemDatabaseView.this.mDbFreshTime, 1);
                        ItemDatabaseView.this.updateData(ItemDatabaseView.this.mDatabaseInfoStr);
                        ItemDatabaseView.this.showData();
                    }
                }
            };
        }
    }

    private void showTip() {
        post(new ItemDatabaseView$$Lambda$1(this));
    }

    /* access modifiers changed from: package-private */
    public final /* synthetic */ void lambda$showTip$1$ItemDatabaseView() {
        setTip(this.mContext.getString(C0213R.string.db_tip1));
    }

    private void handleMultiPage(int dataRowCount) {
        if (!this.mIsMultiPageStarted) {
            initMultiPage();
        } else {
            checkDataLength(dataRowCount);
        }
    }

    private void handleSinglePage() {
        if (this.mUpdateHandler == null) {
            Log.e(TAG, "handleMultiPage: mHandler == null. " + Thread.currentThread().getName());
            return;
        }
        this.mUpdateHandler.removeMessages(0);
        this.mIsMultiPageStarted = false;
        showOnePage();
    }

    /* access modifiers changed from: private */
    /* access modifiers changed from: public */
    private void updateData(String databaseInfoStr) {
        if (TextUtils.isEmpty(databaseInfoStr)) {
            Log.e(TAG, "queryDatabase: Input is null, ignore.");
            return;
        }
        DatabaseQuery databaseQuery = getQueryInstance((DatabaseInfo) new Gson().fromJson(databaseInfoStr, DatabaseInfo.class));
        if (databaseQuery != null) {
            databaseQuery.query();
        }
    }

    private DatabaseQuery getQueryInstance(DatabaseInfo databaseInfo) {
        switch (databaseInfo.getType()) {
            case 0:
                Log.e(TAG, "queryDatabase: Oracle database connector has nit yet been developed...");
                break;
            case 1:
                return new SQLServerQuery(databaseInfo);
            case 2:
                return new MySQLQuery(databaseInfo);
            case 3:
                Log.e(TAG, "queryDatabase: Odbc database connector has nit yet been developed...");
                break;
            case 4:
                Log.e(TAG, "queryDatabase: Access database connector has nit yet been developed...");
                break;
            case 5:
                Log.e(TAG, "queryDatabase: Clone database connector has nit yet been developed...");
                break;
            default:
                Log.e(TAG, "queryDatabase: Unrecognized database type.");
                break;
        }
        return null;
    }

    /* access modifiers changed from: protected */
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (this.mDuration >= 0) {
            removeCallbacks(this);
            postDelayed(this, this.mDuration);
        }
        this.mNetworkConnectReceiver = new NetworkConnectReceiver(this);
        registerNetworkConnectReceiver();
    }

    /* access modifiers changed from: protected */
    public void onDetachedFromWindow() {
        removeCallbacks(this);
        unRegisterNetworkConnectReceiver();
        super.onDetachedFromWindow();
        quitQueryHandlerThread();
        if (this.mUpdateHandler != null) {
            this.mUpdateHandler.removeCallbacksAndMessages(null);
            this.mUpdateHandler = null;
        }
        if (this.mQueryHandler != null) {
            this.mQueryHandler.removeCallbacksAndMessages(null);
            this.mQueryHandler = null;
        }
    }

    private void quitQueryHandlerThread() {
        if (this.mHandlerThread != null) {
            this.mHandlerThread.quit();
            this.mHandlerThread = null;
        }
    }

    private void registerNetworkConnectReceiver() {
        if (this.mNetworkConnectReceiver != null) {
            IntentFilter filter = new IntentFilter();
            filter.addAction("android.net.conn.CONNECTIVITY_CHANGE");
            this.mContext.registerReceiver(this.mNetworkConnectReceiver, filter);
        }
    }

    private void unRegisterNetworkConnectReceiver() {
        if (this.mNetworkConnectReceiver != null) {
            this.mContext.unregisterReceiver(this.mNetworkConnectReceiver);
        }
    }

    private void initDisplay() {
        if (this.mRegion == null) {
            Log.e(TAG, "setItem: mRegion is null.");
        } else if (this.mRegion.rect == null) {
            Log.e(TAG, "setItem: mRegion.rect is null.");
        } else {
            WindowManager.LayoutParams params = new WindowManager.LayoutParams();
            if (this.mRegion.rect.width != null) {
                params.width = Integer.parseInt(this.mRegion.rect.width);
            } else {
                Log.e(TAG, "setItem: mRegion.rect.width is null");
            }
            if (this.mRegion.rect.height != null) {
                params.height = Integer.parseInt(this.mRegion.rect.height);
            } else {
                Log.e(TAG, "setItem: mRegion.rect.height is null");
            }
            setLayoutParams(params);
        }
        if (this.mItem == null) {
            Log.e(TAG, "setItem: mItem is null.");
        } else {
            if (this.mItem.dbLineNumPerPage != null) {
                this.LINE_PER_PAGE = Integer.parseInt(this.mItem.dbLineNumPerPage);
            } else {
                Log.e(TAG, "initDisplay: mItem.dbLineNumPerPage is null!");
            }
            if (this.mItem.dbGridData != null) {
                if ("1".equals(this.mItem.dbGridData.showTitle)) {
                    this.isShowTitle = true;
                }
                if ("1".equals(this.mItem.dbGridData.showHeader)) {
                    this.mIsShowHeader = true;
                }
                if (this.mItem.dbGridData.arRowHeight == null) {
                    Log.e(TAG, "initDisplay: mItem.dbGridData.arRowHeight is null!");
                }
                if (this.mItem.dbGridData.arColWidth == null) {
                    Log.e(TAG, "initDisplay: mItem.dbGridData.arColWidth is null!");
                }
                if (this.mItem.dbRefreshTime != null) {
                    this.mDbFreshTime = Integer.parseInt(this.mItem.dbRefreshTime);
                }
                if (this.mItem.dbGridData.titleMargin != null) {
                    this.mTitleMargin = Integer.parseInt(this.mItem.dbGridData.titleMargin);
                }
                if (this.mItem.dbGridData.borderOuterWidth != null) {
                    this.mBorderOuterWidth = Integer.parseInt(this.mItem.dbGridData.borderOuterWidth);
                }
                if (this.mItem.dbGridData.borderCellWidth != null) {
                    this.mBorderCellWidth = Integer.parseInt(this.mItem.dbGridData.borderCellWidth);
                }
                if (this.mItem.dbGridData.borderOuterClr != null) {
                    this.mBorderOuterClr = this.mItem.dbGridData.borderOuterClr;
                }
                if (this.mItem.dbGridData.borderCellClr != null) {
                    this.mBorderCellClr = this.mItem.dbGridData.borderCellClr;
                }
                if (this.mItem.dbGridData.listFontFormat != null) {
                    for (ProgramParser.SFontFormat fontFormat : this.mItem.dbGridData.listFontFormat.mSFontFormatList) {
                        this.mSFontFormatMap.put(fontFormat.row + fontFormat.col, fontFormat);
                    }
                }
            } else {
                Log.e(TAG, "initDisplay: mItem.dbGridData is null!");
            }
            if (this.mItem.updateInterval == null) {
                Log.e(TAG, "initDisplay: mItem.updateInterval is null");
            } else {
                this.mUpdateInterval = Integer.parseInt(this.mItem.dbPageShowTime);
            }
            if (this.mItem.duration != null) {
                this.mDuration = Long.parseLong(this.mItem.duration);
            } else {
                Log.e(TAG, "setItem: mItem.duration is null");
            }
        }
        if (this.mItem.logfont == null) {
            Log.e(TAG, "setItem: mItem.logfont is null.");
        }
    }

    private void setTitle() {
        if (this.isShowTitle) {
            TextView title = configTextView(this.mItem.dbGridData.titleFontFormat.FontFormat, this.mItem.dbGridData.tableTitle);
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(-2, -2);
            layoutParams.setMargins(0, 0, 0, this.mTitleMargin);
            layoutParams.gravity = 1;
            addView(title, layoutParams);
        }
    }

    private TextView configTextView(ProgramParser.SFontFormat fontFormat, String text) {
        TextView textView = new TextView(this.mContext);
        textView.setTextSize(0, Float.parseFloat(fontFormat.logfont.lfHeight));
        int style = 0;
        if ("1".equals(fontFormat.logfont.lfItalic)) {
            style = 2;
        }
        if ("700".equals(fontFormat.logfont.lfWeight)) {
            style |= 1;
        }
        if ("1".equals(fontFormat.logfont.lfUnderline)) {
            textView.getPaint().setFlags(8);
        }
        Typeface typeface = AppController.getInstance().getTypeface(fontFormat.logfont.lfFaceName);
        if (typeface == null) {
            typeface = Typeface.defaultFromStyle(style);
        }
        textView.setTypeface(typeface, style);
        textView.setText(text);
        textView.setTextColor(GraphUtils.parseColor(fontFormat.textClr));
        if (TextUtils.isEmpty(fontFormat.bkClr) || (!TextUtils.isEmpty(fontFormat.bkClr) && fontFormat.bkClr.startsWith("0x00"))) {
            textView.setBackgroundColor(GraphUtils.parseColor(this.mItem.backcolor));
        } else {
            textView.setBackgroundColor(GraphUtils.parseColor(fontFormat.bkClr));
        }
        if (fontFormat.align.length() > 4) {
            textView.setGravity(getHorAlign(fontFormat.align.substring(fontFormat.align.length() - 1)) | getVerAlign(fontFormat.align.substring(fontFormat.align.length() - 2, fontFormat.align.length() - 1)));
        }
        return textView;
    }

    private int getHorAlign(String horAlign) {
        char c = 65535;
        switch (horAlign.hashCode()) {
            case 49:
                if (horAlign.equals("1")) {
                    c = 0;
                    break;
                }
                break;
            case 52:
                if (horAlign.equals("4")) {
                    c = 1;
                    break;
                }
                break;
        }
        switch (c) {
            case 0:
                return GravityCompat.START;
            case 1:
                return GravityCompat.END;
            default:
                return 1;
        }
    }

    private int getVerAlign(String verAlign) {
        char c = 65535;
        switch (verAlign.hashCode()) {
            case 49:
                if (verAlign.equals("1")) {
                    c = 0;
                    break;
                }
                break;
            case 52:
                if (verAlign.equals("4")) {
                    c = 1;
                    break;
                }
                break;
        }
        switch (c) {
            case 0:
                return 48;
            case 1:
                return 80;
            default:
                return 16;
        }
    }

    private void setTip(String str) {
        TextView errorTip = new TextView(this.mContext);
        errorTip.setTextColor(SupportMenu.CATEGORY_MASK);
        errorTip.setText(str);
        removeAllViews();
        addView(errorTip);
    }

    private DatabaseInfo generateDatabaseInfo() {
        DatabaseInfo databaseInfo = new DatabaseInfo();
        databaseInfo.setType(Integer.parseInt(this.mItem.dbConnection.type));
        databaseInfo.setIp(this.mItem.dbConnection.f14ip);
        databaseInfo.setPort(this.mItem.dbConnection.port);
        databaseInfo.setUsername(this.mItem.dbConnection.username);
        databaseInfo.setPassword(this.mItem.dbConnection.password);
        databaseInfo.setDbname(this.mItem.dbConnection.dbName);
        if ("0".equals(this.mItem.dbSelect.bSql)) {
            databaseInfo.setSql(this.mItem.dbSelect.conditionSql);
        } else {
            databaseInfo.setSql(this.mItem.dbSelect.sql);
        }
        return databaseInfo;
    }

    @Override // com.color.home.widgets.OnPlayFinishObserverable
    public void setListener(OnPlayFinishedListener listener) {
        this.mListener = listener;
    }

    @Override // com.color.home.widgets.OnPlayFinishObserverable
    public void removeListener(OnPlayFinishedListener listener) {
        this.mListener = null;
    }

    public void run() {
        if (this.mListener != null) {
            this.mListener.onPlayFinished(this);
            removeListener(this.mListener);
        }
    }

    @Override // com.color.home.network.ExpandNetworkObserver
    public void networkDisconnect() {
        setTip(this.mContext.getString(C0213R.string.db_tip2));
        Log.e(TAG, "networkDisconnect: Network disconnected.");
        clearStatus();
    }

    private void clearStatus() {
        this.mIsMultiPageStarted = false;
        this.mIsStarted = false;
        if (this.mUpdateHandler != null) {
            this.mUpdateHandler.removeCallbacksAndMessages(null);
        }
        if (this.mQueryHandler != null) {
            this.mQueryHandler.removeCallbacksAndMessages(null);
        }
    }

    @Override // com.color.home.network.NetworkObserver
    public void reloadContent() {
        Log.i(TAG, "reloadContent: network connected.");
        sendHandlerMessage(this.mQueryHandler, 1);
    }

    private void initMultiPage() {
        if (this.mUpdateHandler == null) {
            Log.e(TAG, "handleMultiPage: mHandler == null. " + Thread.currentThread().getName());
            return;
        }
        showNextPage();
        this.mIsMultiPageStarted = true;
        sendHandlerMessageDelayed(this.mUpdateHandler, this.mUpdateInterval, 0);
    }

    private void checkDataLength(int dataRowCount) {
        if (dataRowCount < this.mLastDataRowCount) {
            this.mDataPointer = 0;
        }
    }

    private void showOnePage() {
        this.mDataPointer = 0;
        post(new ItemDatabaseView$$Lambda$2(this));
    }

    /* access modifiers changed from: package-private */
    public final /* synthetic */ void lambda$showOnePage$2$ItemDatabaseView() {
        lambda$showNextPage$0$ItemDatabaseView(this.mDataPointer, this.mDataRowCount);
    }

    /* renamed from: generateTable */
    public void lambda$showNextPage$0$ItemDatabaseView(int startIdx, int endIdx) {
        if (startIdx < 0 || endIdx < 0 || startIdx >= endIdx || endIdx > this.mDataRowCount) {
            Log.e(TAG, "generateTable: Illegal parameter. startIdx=" + startIdx + ", endIdx=" + endIdx);
            return;
        }
        removeAllViews();
        setTitle();
        List<String> headList = DataLab.getInstance().getDataList(0, 1, this.mDatabaseInfoStr);
        List<String> dataList = DataLab.getInstance().getDataList(startIdx + 1, endIdx + 1, this.mDatabaseInfoStr);
        if (headList == null || headList.size() == 0 || dataList == null || dataList.size() == 0) {
            Log.e(TAG, "generateTable: Data acquisition error! headList=" + headList + ", dataList=" + dataList);
            return;
        }
        readTableData(headList.get(0), "header", 0, 0);
        for (int i = 0; i < dataList.size(); i++) {
            readTableData(dataList.get(i), "data", i, dataList.size());
            if (this.mDataArr[i] == null) {
                Log.e(TAG, "generateTable: Data in mDataArr at line " + i + " is null.");
            }
        }
        showView(dataList.size());
    }

    private void showView(int endIdx) {
        if (this.mHeaderArr == null || this.mDataArr == null) {
            Log.e(TAG, "showView: Fail to read table data!");
            return;
        }
        TableLayout tableLayout = generateTableLayout(0, endIdx);
        LinearLayout tableWrapper = generateTableWrapper();
        tableWrapper.addView(tableLayout);
        addView(tableWrapper);
    }

    private TableLayout generateTableLayout(int startIdx, int endIdx) {
        TableLayout tableLayout = new TableLayout(this.mContext);
        if (this.mIsShowHeader) {
            tableLayout.addView(generateRow(this.mHeaderArr, 0, 0));
        }
        for (int i = startIdx; i < endIdx; i++) {
            if (i == endIdx - 1) {
                tableLayout.addView(generateRow(this.mDataArr[i], 2, (i - startIdx) + 1));
            } else if (i != 0 || this.mIsShowHeader) {
                tableLayout.addView(generateRow(this.mDataArr[i], 1, (i - startIdx) + 1));
            } else {
                tableLayout.addView(generateRow(this.mDataArr[i], 0, (i - startIdx) + 1));
            }
        }
        return tableLayout;
    }

    private LinearLayout generateTableWrapper() {
        LinearLayout tableWrapper = new LinearLayout(this.mContext);
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(-2, -2);
        params.gravity = 1;
        int padding = this.mBorderOuterWidth;
        if (padding == 0) {
            tableWrapper.setBackgroundColor(0);
        } else {
            tableWrapper.setBackgroundColor(GraphUtils.parseColor(this.mBorderOuterClr));
            tableWrapper.setPadding(padding, padding, padding, padding);
        }
        tableWrapper.setLayoutParams(params);
        return tableWrapper;
    }

    private void readTableData(String line, String type, int index, int rowCount) {
        if ("header".equals(type)) {
            this.mHeaderArr = line.split(DELIMITER);
            for (int i = 0; i < this.mHeaderArr.length; i++) {
                if (!TextUtils.isEmpty(this.mItem.colShowArray.colArrays.get(i).reColName)) {
                    this.mHeaderArr[i] = this.mItem.colShowArray.colArrays.get(i).reColName;
                } else {
                    int start = this.mHeaderArr[i].indexOf("\"");
                    int end = this.mHeaderArr[i].lastIndexOf("\"");
                    if (end > start + 1) {
                        this.mHeaderArr[i] = this.mHeaderArr[i].substring(start + 1, end);
                    }
                }
            }
        } else if ("data".equals(type)) {
            if (this.mDataArr == null) {
                this.mDataArr = (String[][]) Array.newInstance(String.class, rowCount, this.mHeaderArr.length);
            } else if (rowCount != this.mDataArr.length) {
                this.mDataArr = (String[][]) Array.newInstance(String.class, rowCount, this.mHeaderArr.length);
            }
            String[] strArr = line.split(DELIMITER);
            for (int i2 = 0; i2 < strArr.length; i2++) {
                if (strArr[i2].equalsIgnoreCase("null")) {
                    strArr[i2] = "";
                }
            }
            System.arraycopy(strArr, 0, this.mDataArr[index], 0, Math.min(strArr.length, this.mDataArr[index].length));
            if (strArr.length < this.mHeaderArr.length) {
                for (int i3 = strArr.length; i3 < this.mHeaderArr.length; i3++) {
                    this.mDataArr[index][i3] = "";
                }
            }
        } else {
            Log.e(TAG, "readTableData: Wrong parameter.");
        }
    }

    private TableRow generateRow(String[] line, int type, int rowNumber) {
        TableRow row = new TableRow(this.mContext);
        for (int i = 0; i < line.length; i++) {
            TextView textView = generateCell(rowNumber, i, line[i]);
            if (this.mBorderCellWidth == 0) {
                LinearLayout wrapper = new LinearLayout(this.mContext);
                wrapper.addView(textView);
                row.addView(wrapper);
            } else {
                LinearLayout wrapper2 = generateCellWrapper(type, i, line.length);
                wrapper2.addView(textView);
                row.addView(wrapper2);
            }
        }
        return row;
    }

    private TextView generateCell(int row, int col, String text) {
        ProgramParser.SFontFormat fontFormat;
        if (this.mSFontFormatMap.containsKey(row + "" + col)) {
            fontFormat = this.mSFontFormatMap.get(row + "" + col);
        } else {
            fontFormat = this.mItem.dbGridData.defaultFontFormat.sFontFormat;
        }
        TextView textView = configTextView(fontFormat, text);
        textView.setWidth(getCellWidth(col));
        textView.setHeight(getCellHeight(row));
        return textView;
    }

    private int getCellHeight(int row) {
        List<String> rowHeightList = this.mItem.dbGridData.arRowHeight.rowHeight;
        if (!rowHeightList.isEmpty()) {
            return row < rowHeightList.size() ? Integer.parseInt(rowHeightList.get(row)) : Integer.parseInt(rowHeightList.get(rowHeightList.size() - 1));
        }
        return 50;
    }

    private int getCellWidth(int col) {
        List<String> rowWidthList = this.mItem.dbGridData.arColWidth.colWidth;
        if (!rowWidthList.isEmpty()) {
            return col < rowWidthList.size() ? Integer.parseInt(rowWidthList.get(col)) : Integer.parseInt(rowWidthList.get(rowWidthList.size() - 1));
        }
        return 120;
    }

    private LinearLayout generateCellWrapper(int type, int index, int colLength) {
        LinearLayout wrapper = new LinearLayout(this.mContext);
        wrapper.setBackgroundColor(GraphUtils.parseColor(this.mBorderCellClr));
        int padding = this.mBorderCellWidth;
        if (type == 0 || 1 == type) {
            if (index == colLength - 1) {
                wrapper.setPadding(0, 0, 0, padding);
            } else {
                wrapper.setPadding(0, 0, padding, padding);
            }
        } else if (index != colLength - 1) {
            wrapper.setPadding(0, 0, padding, 0);
        }
        return wrapper;
    }
}
