#
#   Copyright 2004 Sun Microsystems, Inc.
#
#   Licensed under the Apache License, Version 2.0 (the "License");
#   you may not use this file except in compliance with the License.
#   You may obtain a copy of the License at
#
#       http://www.apache.org/licenses/LICENSE-2.0
#
#   Unless required by applicable law or agreed to in writing, software
#   distributed under the License is distributed on an "AS IS" BASIS,
#   WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#   See the License for the specific language governing permissions and
#   limitations under the License.
#


# Feed Parser implementation classes
#
WireFeedParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS090Parser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS091NetscapeParser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS091UserlandParser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS092Parser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS093Parser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS094Parser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS10Parser  \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS20wNSParser  \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS20Parser  \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.Atom10Parser \
                       com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.Atom03Parser 

# Parsers for Atom 1.0 feed modules
#
atom_1.0.feed.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.SyModuleParser \
                                   com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for Atom 1.0 entry modules
#
atom_1.0.item.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for Atom 0.3 feed modules
#
atom_0.3.feed.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.SyModuleParser \
                                   com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for Atom 0.3 entry modules
#
atom_0.3.item.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for RSS 1.0 feed modules
#
rss_1.0.feed.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.SyModuleParser \
                                  com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for RSS 1.0 item modules
#
rss_1.0.item.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for RSS 2.0 (w/NS) feed modules
#
rss_2.0wNS.feed.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for RSS 2.0 (w/NS) item modules
#
rss_2.0wNS.item.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for RSS 2.0 feed modules
#
rss_2.0.feed.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser

# Parsers for RSS 2.0 item modules
#
rss_2.0.item.ModuleParser.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleParser




# Feed Generator implementation classes
#
WireFeedGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS090Generator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS091NetscapeGenerator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS091UserlandGenerator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS092Generator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS093Generator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS094Generator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS10Generator  \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.RSS20Generator  \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.Atom10Generator \
                          com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.Atom03Generator 

# Generators for Atom 1.0 feed modules
#
atom_1.0.feed.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.SyModuleGenerator \
                                      com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for Atom 1.0 entry modules
#
atom_1.0.item.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for Atom 0.3 feed modules
#
atom_0.3.feed.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.SyModuleGenerator \
                                      com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for Atom 0.3 entry modules
#
atom_0.3.item.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for RSS 1.0 feed modules
#
rss_1.0.feed.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.SyModuleGenerator \
                                     com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for RSS_1.0 entry modules
#
rss_1.0.item.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for RSS 2.0 feed modules
#
rss_2.0.feed.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator

# Generators for RSS_2.0 entry modules
#
rss_2.0.item.ModuleGenerator.classes=com.google.code.rome.android.repackaged.com.sun.syndication.io.impl.DCModuleGenerator




# Feed Conversor implementation classes
#
Converter.classes=com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForAtom10 \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForAtom03 \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS090 \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS091Netscape \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS091Userland \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS092 \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS093 \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS094 \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS10  \
                  com.google.code.rome.android.repackaged.com.sun.syndication.feed.synd.impl.ConverterForRSS20 



