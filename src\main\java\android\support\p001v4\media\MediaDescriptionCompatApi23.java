package android.support.p001v4.media;

import android.media.MediaDescription;
import android.net.Uri;
import android.support.annotation.RequiresApi;
import android.support.p001v4.media.MediaDescriptionCompatApi21;

/* access modifiers changed from: package-private */
@RequiresApi(23)
/* renamed from: android.support.v4.media.MediaDescriptionCompatApi23 */
public class MediaDescriptionCompatApi23 extends MediaDescriptionCompatApi21 {
    MediaDescriptionCompatApi23() {
    }

    public static Uri getMediaUri(Object descriptionObj) {
        return ((MediaDescription) descriptionObj).getMediaUri();
    }

    /* access modifiers changed from: package-private */
    /* renamed from: android.support.v4.media.MediaDescriptionCompatApi23$Builder */
    public static class Builder extends MediaDescriptionCompatApi21.Builder {
        Builder() {
        }

        public static void setMediaUri(Object builderObj, Uri mediaUri) {
            ((MediaDescription.Builder) builderObj).setMediaUri(mediaUri);
        }
    }
}
